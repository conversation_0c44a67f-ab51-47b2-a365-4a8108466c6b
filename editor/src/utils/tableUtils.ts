import type {
	LogicalBorder,
	TableBorderState,
	TableCell,
	TableCellBorder,
	TableProperties,
} from "@/types/table";
import { pxToMm } from "./unitConversion";

/**
 * Represents a resolved border between two cells
 */
export interface ResolvedBorder {
	width: number;
	color: string;
	style: string;
}

/**
 * Resolves border conflicts between adjacent cells by applying priority rules
 */
export function resolveBorderConflict(
	border1: TableCellBorder | undefined,
	border2: TableCellBorder | undefined,
	globalBorderColor: string = "#000000",
	defaultWidth: number = 1,
): ResolvedBorder {
	// If neither cell has a border setting, use defaults
	if (!border1 && !border2) {
		return {
			width: defaultWidth,
			color: globalBorderColor,
			style: "solid",
		};
	}

	// If only one cell has a border setting, use it
	if (border1 && !border2) {
		return {
			width: border1.width || defaultWidth,
			color: border1.color || globalBorderColor,
			style: "solid",
		};
	}

	if (border2 && !border1) {
		return {
			width: border2.width || defaultWidth,
			color: border2.color || globalBorderColor,
			style: "solid",
		};
	}

	// Both cells have border settings - apply priority rules
	if (border1 && border2) {
		const width1 = border1.width || defaultWidth;
		const width2 = border2.width || defaultWidth;

		// Priority 1: Use the thicker border
		if (width1 > width2) {
			return {
				width: width1,
				color: border1.color || globalBorderColor,
				style: "solid",
			};
		} else if (width2 > width1) {
			return {
				width: width2,
				color: border2.color || globalBorderColor,
				style: "solid",
			};
		}

		// Priority 2: If same width, prefer the first border (left/top cell takes precedence)
		return {
			width: width1,
			color: border1.color || globalBorderColor,
			style: "solid",
		};
	}

	// Fallback (should never reach here)
	return {
		width: defaultWidth,
		color: globalBorderColor,
		style: "solid",
	};
}

/**
 * Helper function to calculate maximum columns in a table
 */
function getMaxColumns(cells: TableCell[][]): number {
	let maxCols = 0;
	for (const row of cells) {
		let colCount = 0;
		for (const cell of row) {
			colCount += cell.colspan || 1;
		}
		maxCols = Math.max(maxCols, colCount);
	}
	return maxCols;
}

/**
 * Gets the resolved borders for a cell, taking into account conflicts with adjacent cells
 */
export function getResolvedCellBorders(
	cells: TableCell[][],
	rowIndex: number,
	colIndex: number,
	globalBorderColor: string = "#000000",
): {
	top: ResolvedBorder;
	right: ResolvedBorder;
	bottom: ResolvedBorder;
	left: ResolvedBorder;
} {
	const cell = cells[rowIndex]?.[colIndex];
	if (!cell) {
		const defaultBorder = {
			width: 1,
			color: globalBorderColor,
			style: "solid",
		};
		return {
			top: defaultBorder,
			right: defaultBorder,
			bottom: defaultBorder,
			left: defaultBorder,
		};
	}

	// Helper function to get effective border from cell
	const getEffectiveBorder = (
		cell: TableCell,
		side: "top" | "right" | "bottom" | "left",
	): TableCellBorder | undefined => {
		const borderSetting = cell.borderSettings?.[side];
		const borderWidth = cell.borderWidths[side];

		if (borderSetting) {
			return borderSetting;
		}

		if (borderWidth > 0) {
			return {
				width: borderWidth,
				color: globalBorderColor,
			};
		}

		return undefined;
	};

	// Calculate logical grid position for this cell
	const maxColumns = getMaxColumns(cells);
	const logicalGrid = createLogicalGrid(cells, cells.length, maxColumns);
	const { logicalRow, logicalCol } = cellToLogicalPosition(
		cells,
		rowIndex,
		colIndex,
	);

	// Helper function to get cell from logical grid position
	const getCellFromLogicalPosition = (
		logicalRow: number,
		logicalCol: number,
	): TableCell | null => {
		const cellRef = logicalGrid[logicalRow]?.[logicalCol];
		if (!cellRef) return null;
		return cells[cellRef.rowIndex]?.[cellRef.colIndex] || null;
	};

	// Get adjacent cells in logical grid
	const topNeighbor =
		logicalRow > 0
			? getCellFromLogicalPosition(logicalRow - 1, logicalCol)
			: null;
	const rightNeighbor = getCellFromLogicalPosition(
		logicalRow,
		logicalCol + cell.colspan,
	);
	const bottomNeighbor = getCellFromLogicalPosition(
		logicalRow + cell.rowspan,
		logicalCol,
	);
	const leftNeighbor =
		logicalCol > 0
			? getCellFromLogicalPosition(logicalRow, logicalCol - 1)
			: null;

	// Helper function to get border, preferring explicit settings over conflict resolution
	const getBorderWithFallback = (
		side: "top" | "right" | "bottom" | "left",
		neighbor: TableCell | null,
		neighborSide: "top" | "right" | "bottom" | "left",
	): ResolvedBorder => {
		const cellBorder = getEffectiveBorder(cell, side);

		// If this cell has an explicit border setting, use it
		if (cellBorder) {
			return {
				width: cellBorder.width || cell.borderWidths[side],
				color: cellBorder.color || globalBorderColor,
				style: "solid",
			};
		}

		// If no explicit setting, try to resolve with neighbor
		const neighborBorder = neighbor
			? getEffectiveBorder(neighbor, neighborSide)
			: undefined;
		return resolveBorderConflict(
			undefined, // No border from this cell
			neighborBorder,
			globalBorderColor,
			cell.borderWidths[side],
		);
	};

	const topBorder = getBorderWithFallback("top", topNeighbor, "bottom");
	const rightBorder = getBorderWithFallback("right", rightNeighbor, "left");
	const bottomBorder = getBorderWithFallback("bottom", bottomNeighbor, "top");
	const leftBorder = getBorderWithFallback("left", leftNeighbor, "right");

	return {
		top: topBorder,
		right: rightBorder,
		bottom: bottomBorder,
		left: leftBorder,
	};
}

/**
 * Creates an initial border state from table properties
 */
export function createInitialBorderState(
	tableProperties: TableProperties,
): TableBorderState {
	const { rows, columns, cells, borderColor = "#000000" } = tableProperties;

	// Initialize border arrays
	const horizontal: (LogicalBorder | null)[][] = [];
	const vertical: (LogicalBorder | null)[][] = [];

	// Create horizontal borders (below each cell)
	for (let row = 0; row < rows; row++) {
		horizontal[row] = [];
		for (let col = 0; col < columns; col++) {
			horizontal[row][col] = null; // Will be populated from cell borders
		}
	}

	// Create vertical borders (to the right of each cell)
	for (let row = 0; row < rows; row++) {
		vertical[row] = [];
		for (let col = 0; col < columns; col++) {
			vertical[row][col] = null; // Will be populated from cell borders
		}
	}

	// Populate from existing cell border settings
	cells.forEach((row, rowIndex) => {
		row.forEach((cell, colIndex) => {
			const { logicalRow, logicalCol } = cellToLogicalPosition(
				cells,
				rowIndex,
				colIndex,
			);

			// Bottom border
			if (cell.borderSettings?.bottom || cell.borderWidths.bottom > 0) {
				const width =
					cell.borderSettings?.bottom?.width ?? cell.borderWidths.bottom;
				const color = cell.borderSettings?.bottom?.color ?? borderColor;
				// Store border for all logical rows
				if (logicalRow < rows) {
					horizontal[logicalRow][logicalCol] = {
						width,
						color,
						style: "solid",
					};
				}
			}

			// Right border
			if (cell.borderSettings?.right || cell.borderWidths.right > 0) {
				const width =
					cell.borderSettings?.right?.width ?? cell.borderWidths.right;
				const color = cell.borderSettings?.right?.color ?? borderColor;
				// Store border for all logical columns
				if (logicalCol < columns) {
					vertical[logicalRow][logicalCol] = {
						width,
						color,
						style: "solid",
					};
				}
			}
		});
	});

	return { horizontal, vertical };
}

/**
 * Gets the logical border for rendering a specific cell side
 */

export function getLogicalBorder(
	borderState: TableBorderState,
	logicalRow: number,
	logicalCol: number,
	side: "top" | "right" | "bottom" | "left",
	tableProperties: TableProperties,
): LogicalBorder {
	const defaultBorder: LogicalBorder = {
		width: 1,
		color: tableProperties.borderColor || "#000000",
		style: "solid",
	};

	const hiddenBorder: LogicalBorder = {
		width: 0,
		color: tableProperties.borderColor || "#000000",
		style: "solid",
	};

	switch (side) {
		case "top":
			if (logicalRow > 0) {
				const border = borderState.horizontal[logicalRow - 1]?.[logicalCol];
				if (border === null) return hiddenBorder;
				return border || defaultBorder;
			}
			return defaultBorder;

		case "bottom":
			// For bottom border, check if we're within the logical border array bounds
			if (logicalRow < borderState.horizontal.length) {
				const border = borderState.horizontal[logicalRow]?.[logicalCol];
				if (border === null) return hiddenBorder;
				return border || defaultBorder;
			}
			return defaultBorder;

		case "left":
			if (logicalCol > 0) {
				const border = borderState.vertical[logicalRow]?.[logicalCol - 1];
				if (border === null) return hiddenBorder;
				return border || defaultBorder;
			}
			return defaultBorder;

		case "right":
			// For right border, check if we're within the logical border array bounds
			if (logicalCol < borderState.vertical[logicalRow]?.length) {
				const border = borderState.vertical[logicalRow]?.[logicalCol];
				if (border === null) return hiddenBorder;
				return border || defaultBorder;
			}
			return defaultBorder;
	}
}

/**
 * Applies border changes to the logical border state
 */
export function applyBordersToLogicalState(
	borderState: TableBorderState,
	selection: {
		start: { row: number; col: number };
		end: { row: number; col: number };
	},
	activeLines: {
		outerTop: boolean;
		outerBottom: boolean;
		outerLeft: boolean;
		outerRight: boolean;
		innerHorizontal: boolean;
		innerVertical: boolean;
	},
	widthPx: number | null,
	colorHex: string | null,
	tableProperties: TableProperties,
): TableBorderState {
	const newBorderState: TableBorderState = {
		horizontal: borderState.horizontal.map((row) => [...row]),
		vertical: borderState.vertical.map((row) => [...row]),
	};

	const R_START = Math.min(selection.start.row, selection.end.row);
	const R_END = Math.max(selection.start.row, selection.end.row);
	const C_START = Math.min(selection.start.col, selection.end.col);
	const C_END = Math.max(selection.start.col, selection.end.col);
	// Build logical grid for mapping logical positions to cell indices
	const logicalGrid = createLogicalGrid(
		tableProperties.cells,
		tableProperties.rows,
		tableProperties.columns,
	);

	const defaultColor = tableProperties.borderColor || "#000000";
	const borderColor = colorHex || defaultColor;
	const borderWidth = widthPx || 1;

	// Apply outer top border: if not topmost, update logical border state; otherwise set cell borderSettings.top
	if (activeLines.outerTop) {
		if (R_START > 0) {
			for (let col = C_START; col <= C_END; col++) {
				if (widthPx === 0) {
					newBorderState.horizontal[R_START - 1][col] = null;
				} else {
					newBorderState.horizontal[R_START - 1][col] = {
						width: borderWidth,
						color: borderColor,
						style: "solid",
					};
				}
			}
		} else {
			// First row: set explicit top border on each cell in logical row 0
			for (let col = C_START; col <= C_END; col++) {
				const cellRef = logicalGrid[0][col];
				if (cellRef) {
					const cell =
						tableProperties.cells[cellRef.rowIndex][cellRef.colIndex];
					if (widthPx === 0) {
						// Remove border setting and set borderWidth to 0
						cell.borderSettings = cell.borderSettings || {};
						delete cell.borderSettings.top;
						cell.borderWidths.top = 0;
						// Clean up empty borderSettings
						if (Object.keys(cell.borderSettings).length === 0) {
							delete cell.borderSettings;
						}
					} else {
						cell.borderSettings = {
							...(cell.borderSettings || {}),
							top: { width: borderWidth, color: borderColor },
						};
					}
				}
			}
		}
	}

	// Apply outer bottom border: if not last row, update logical border state; otherwise set cell borderSettings.bottom
	if (activeLines.outerBottom) {
		if (R_END < newBorderState.horizontal.length) {
			// Not the last row: update logical border state
			for (let col = C_START; col <= C_END; col++) {
				if (widthPx === 0) {
					newBorderState.horizontal[R_END][col] = null;
				} else {
					newBorderState.horizontal[R_END][col] = {
						width: borderWidth,
						color: borderColor,
						style: "solid",
					};
				}
			}
		} else {
			// Last row: set explicit bottom border on each cell in the last logical row
			for (let col = C_START; col <= C_END; col++) {
				const cellRef = logicalGrid[R_END][col];
				if (cellRef) {
					const cell =
						tableProperties.cells[cellRef.rowIndex][cellRef.colIndex];
					if (widthPx === 0) {
						// Remove border setting and set borderWidth to 0
						cell.borderSettings = cell.borderSettings || {};
						delete cell.borderSettings.bottom;
						cell.borderWidths.bottom = 0;
						// Clean up empty borderSettings
						if (Object.keys(cell.borderSettings).length === 0) {
							delete cell.borderSettings;
						}
					} else {
						cell.borderSettings = {
							...(cell.borderSettings || {}),
							bottom: { width: borderWidth, color: borderColor },
						};
					}
				}
			}
		}
	}

	// Apply outer left border: if not leftmost, update logical border state; otherwise set cell borderSettings.left
	if (activeLines.outerLeft) {
		if (C_START > 0) {
			for (let row = R_START; row <= R_END; row++) {
				if (widthPx === 0) {
					newBorderState.vertical[row][C_START - 1] = null;
				} else {
					newBorderState.vertical[row][C_START - 1] = {
						width: borderWidth,
						color: borderColor,
						style: "solid",
					};
				}
			}
		} else {
			// First column: set explicit left border on each cell in logical col 0
			for (let row = R_START; row <= R_END; row++) {
				const cellRef = logicalGrid[row][0];
				if (cellRef) {
					const cell =
						tableProperties.cells[cellRef.rowIndex][cellRef.colIndex];
					if (widthPx === 0) {
						// Remove border setting and set borderWidth to 0
						cell.borderSettings = cell.borderSettings || {};
						delete cell.borderSettings.left;
						cell.borderWidths.left = 0;
						// Clean up empty borderSettings
						if (Object.keys(cell.borderSettings).length === 0) {
							delete cell.borderSettings;
						}
					} else {
						cell.borderSettings = {
							...(cell.borderSettings || {}),
							left: { width: borderWidth, color: borderColor },
						};
					}
				}
			}
		}
	}

	// Apply outer right border: if not last column, update logical border state; otherwise set cell borderSettings.right
	if (activeLines.outerRight) {
		if (C_END < newBorderState.vertical[0]?.length) {
			// Not the last column: update logical border state
			for (let row = R_START; row <= R_END; row++) {
				if (widthPx === 0) {
					newBorderState.vertical[row][C_END] = null;
				} else {
					newBorderState.vertical[row][C_END] = {
						width: borderWidth,
						color: borderColor,
						style: "solid",
					};
				}
			}
		} else {
			// Last column: set explicit right border on each cell in the last logical column
			for (let row = R_START; row <= R_END; row++) {
				const cellRef = logicalGrid[row][C_END];
				if (cellRef) {
					const cell =
						tableProperties.cells[cellRef.rowIndex][cellRef.colIndex];
					if (widthPx === 0) {
						// Remove border setting and set borderWidth to 0
						cell.borderSettings = cell.borderSettings || {};
						delete cell.borderSettings.right;
						cell.borderWidths.right = 0;
						// Clean up empty borderSettings
						if (Object.keys(cell.borderSettings).length === 0) {
							delete cell.borderSettings;
						}
					} else {
						cell.borderSettings = {
							...(cell.borderSettings || {}),
							right: { width: borderWidth, color: borderColor },
						};
					}
				}
			}
		}
	}

	// Apply inner borders
	if (activeLines.innerHorizontal) {
		for (let row = R_START; row < R_END; row++) {
			for (let col = C_START; col <= C_END; col++) {
				if (widthPx === 0) {
					newBorderState.horizontal[row][col] = null;
				} else {
					newBorderState.horizontal[row][col] = {
						width: borderWidth,
						color: borderColor,
						style: "solid",
					};
				}
			}
		}
	}

	if (activeLines.innerVertical) {
		for (let row = R_START; row <= R_END; row++) {
			for (let col = C_START; col < C_END; col++) {
				if (widthPx === 0) {
					newBorderState.vertical[row][col] = null;
				} else {
					newBorderState.vertical[row][col] = {
						width: borderWidth,
						color: borderColor,
						style: "solid",
					};
				}
			}
		}
	}

	return newBorderState;
}

/**
 * Gets the borders for a cell using the logical border state
 */
export function getCellBordersFromLogicalState(
	tableProperties: TableProperties,
	rowIndex: number,
	colIndex: number,
): {
	top: ResolvedBorder;
	right: ResolvedBorder;
	bottom: ResolvedBorder;
	left: ResolvedBorder;
} {
	// Initialize border state if it doesn't exist
	if (!tableProperties.borderState) {
		tableProperties.borderState = createInitialBorderState(tableProperties);
	}

	const { logicalRow, logicalCol } = cellToLogicalPosition(
		tableProperties.cells,
		rowIndex,
		colIndex,
	);
	const cell = tableProperties.cells[rowIndex][colIndex];
	// Determine top border: explicit setting on cell takes precedence
	let topBorder: ResolvedBorder;
	if (cell.borderSettings?.top) {
		topBorder = {
			width: cell.borderSettings.top.width,
			color:
				cell.borderSettings.top.color ||
				tableProperties.borderColor ||
				"#000000",
			style: "solid",
		};
	} else if (cell.borderWidths.top === 0) {
		// Border was explicitly set to 0 (hidden)
		topBorder = {
			width: 0,
			color: tableProperties.borderColor || "#000000",
			style: "solid",
		};
	} else {
		topBorder = getLogicalBorder(
			tableProperties.borderState,
			logicalRow,
			logicalCol,
			"top",
			tableProperties,
		);
	}

	// Determine right border: check for explicit cell setting first
	let rightBorder: ResolvedBorder;
	if (cell.borderSettings?.right) {
		rightBorder = {
			width: cell.borderSettings.right.width,
			color:
				cell.borderSettings.right.color ||
				tableProperties.borderColor ||
				"#000000",
			style: "solid",
		};
	} else if (cell.borderWidths.right === 0) {
		// Border was explicitly set to 0 (hidden)
		rightBorder = {
			width: 0,
			color: tableProperties.borderColor || "#000000",
			style: "solid",
		};
	} else {
		rightBorder = getLogicalBorder(
			tableProperties.borderState,
			logicalRow,
			logicalCol,
			"right",
			tableProperties,
		);
	}

	// Determine bottom border: check for explicit cell setting first
	let bottomBorder: ResolvedBorder;
	if (cell.borderSettings?.bottom) {
		bottomBorder = {
			width: cell.borderSettings.bottom.width,
			color:
				cell.borderSettings.bottom.color ||
				tableProperties.borderColor ||
				"#000000",
			style: "solid",
		};
	} else if (cell.borderWidths.bottom === 0) {
		// Border was explicitly set to 0 (hidden)
		bottomBorder = {
			width: 0,
			color: tableProperties.borderColor || "#000000",
			style: "solid",
		};
	} else {
		bottomBorder = getLogicalBorder(
			tableProperties.borderState,
			logicalRow,
			logicalCol,
			"bottom",
			tableProperties,
		);
	}

	// Determine left border: explicit setting on cell takes precedence
	let leftBorder: ResolvedBorder;
	if (cell.borderSettings?.left) {
		leftBorder = {
			width: cell.borderSettings.left.width,
			color:
				cell.borderSettings.left.color ||
				tableProperties.borderColor ||
				"#000000",
			style: "solid",
		};
	} else if (cell.borderWidths.left === 0) {
		// Border was explicitly set to 0 (hidden)
		leftBorder = {
			width: 0,
			color: tableProperties.borderColor || "#000000",
			style: "solid",
		};
	} else {
		leftBorder = getLogicalBorder(
			tableProperties.borderState,
			logicalRow,
			logicalCol,
			"left",
			tableProperties,
		);
	}

	return {
		top: {
			width: topBorder.width,
			color: topBorder.color,
			style: topBorder.style,
		},
		right: {
			width: rightBorder.width,
			color: rightBorder.color,
			style: rightBorder.style,
		},
		bottom: {
			width: bottomBorder.width,
			color: bottomBorder.color,
			style: bottomBorder.style,
		},
		left: {
			width: leftBorder.width,
			color: leftBorder.color,
			style: leftBorder.style,
		},
	};
}

export function getTableBorderContributions(tableProperties?: TableProperties) {
	if (!tableProperties) {
		return { h: 0, v: 0, left: 0, top: 0, right: 0, bottom: 0 };
	}
	const { cells, rows, columns } = tableProperties;

	let maxLeftBorderPx = 0;
	if (cells) {
		cells.forEach((row) => {
			if (row?.[0]) {
				const cell = row[0];
				const borderWidth =
					cell.borderSettings?.left?.width ?? cell.borderWidths?.left ?? 0;
				if (borderWidth > maxLeftBorderPx) maxLeftBorderPx = borderWidth;
			}
		});
	}

	let maxRightBorderPx = 0;
	if (cells && columns > 0) {
		cells.forEach((row) => {
			let logicalCol = 0;
			for (const cell of row) {
				if (!cell) continue;
				logicalCol += cell.colspan;
				if (logicalCol >= columns) {
					const borderWidth =
						cell.borderSettings?.right?.width ?? cell.borderWidths?.right ?? 0;
					if (borderWidth > maxRightBorderPx) maxRightBorderPx = borderWidth;
					break;
				}
			}
		});
	}

	let maxTopBorderPx = 0;
	if (cells?.[0]) {
		cells[0].forEach((cell) => {
			if (cell) {
				const borderWidth =
					cell.borderSettings?.top?.width ?? cell.borderWidths?.top ?? 0;
				if (borderWidth > maxTopBorderPx) maxTopBorderPx = borderWidth;
			}
		});
	}

	let maxBottomBorderPx = 0;
	if (cells && rows > 0) {
		cells.forEach((row, rowIndex) => {
			row.forEach((cell) => {
				if (cell && rowIndex + cell.rowspan >= rows) {
					const borderWidth =
						cell.borderSettings?.bottom?.width ??
						cell.borderWidths?.bottom ??
						0;
					if (borderWidth > maxBottomBorderPx) maxBottomBorderPx = borderWidth;
				}
			});
		});
	}

	// With border-collapse, half of the border's width extends outwards.
	const horizontalContributionMm = pxToMm(
		(maxLeftBorderPx + maxRightBorderPx) / 2,
	);
	const verticalContributionMm = pxToMm(
		(maxTopBorderPx + maxBottomBorderPx) / 2,
	);
	const leftShiftMm = pxToMm(maxLeftBorderPx / 2);
	const topShiftMm = pxToMm(maxTopBorderPx / 2);

	return {
		h: horizontalContributionMm,
		v: verticalContributionMm,
		left: leftShiftMm,
		top: topShiftMm,
		right: pxToMm(maxRightBorderPx / 2),
		bottom: pxToMm(maxBottomBorderPx / 2),
	};
}

// Calculates scaled column widths and row heights for a table so that it fits into
// the provided container dimensions while preserving the original aspect ratio.
//
// initialColumnWidths / initialRowHeights – intrinsic dimensions in mm stored on the element.
// containerWidthMm / containerHeightMm    – the available space in mm. If omitted the
//                                            intrinsic total size is used which results in
//                                            a scale factor of 1.
//
// The function returns the scaled widths / heights as well as a flag that indicates whether
// the scaling is actually based on *valid* container dimensions (both container and intrinsic
// dimensions > 0).
export function calculateScaledDimensions(
	initialColumnWidths: number[] | undefined,
	initialRowHeights: number[] | undefined,
	containerWidthMm?: number,
	containerHeightMm?: number,
) {
	const intrinsicWidthMm =
		initialColumnWidths?.reduce((sum, w) => sum + w, 0) || 0;
	const intrinsicHeightMm =
		initialRowHeights?.reduce((sum, h) => sum + h, 0) || 0;

	const currentContainerWidthMm = containerWidthMm ?? intrinsicWidthMm;
	const currentContainerHeightMm = containerHeightMm ?? intrinsicHeightMm;

	// Both container and intrinsic dimensions have to be > 0 – otherwise no scaling is applied.
	const hasValidDimensions =
		currentContainerWidthMm > 0 &&
		currentContainerHeightMm > 0 &&
		intrinsicWidthMm > 0 &&
		intrinsicHeightMm > 0;

	// If we do not have valid data fall back to a scale factor of 1.
	const scaleX = hasValidDimensions
		? currentContainerWidthMm / intrinsicWidthMm
		: 1;
	const scaleY = hasValidDimensions
		? currentContainerHeightMm / intrinsicHeightMm
		: 1;

	const scaledWidths = initialColumnWidths
		? initialColumnWidths.map((w) => w * scaleX)
		: [];
	const scaledHeights = initialRowHeights
		? initialRowHeights.map((h) => h * scaleY)
		: [];

	return {
		scaledWidths,
		scaledHeights,
		hasValidDimensions,
		intrinsicWidthMm,
		intrinsicHeightMm,
	} as const;
}

/**
 * Creates a logical grid representation of the table, mapping each logical grid position
 * to the actual cell (rowIndex, colIndex) that occupies that position.
 * This accounts for colspan and rowspan attributes.
 */
export function createLogicalGrid(
	cells: TableCell[][],
	rows: number,
	columns: number,
): Array<Array<{ rowIndex: number; colIndex: number } | null>> {
	// Validate input parameters
	if (!cells || rows <= 0 || columns <= 0) {
		console.warn(
			`Invalid grid parameters: rows=${rows}, columns=${columns}, cells=${cells?.length || 0}`,
		);
		return Array(Math.max(1, rows))
			.fill(null)
			.map(() => Array(Math.max(1, columns)).fill(null));
	}

	// Initialize grid with null values
	const grid: Array<Array<{ rowIndex: number; colIndex: number } | null>> =
		Array(rows)
			.fill(null)
			.map(() => Array(columns).fill(null));

	for (let rowIndex = 0; rowIndex < Math.min(cells.length, rows); rowIndex++) {
		const row = cells[rowIndex];
		if (!row) continue;

		let logicalCol = 0;

		for (let colIndex = 0; colIndex < row.length; colIndex++) {
			const cell = row[colIndex];
			if (!cell) continue;

			// Ensure cell has valid span values
			const cellRowspan = Math.max(1, cell.rowspan || 1);
			const cellColspan = Math.max(1, cell.colspan || 1);

			// Find the next available logical column position
			while (
				logicalCol < columns &&
				grid[rowIndex] &&
				grid[rowIndex][logicalCol] !== null
			) {
				logicalCol++;
			}

			// Skip if we've exceeded the column limit
			if (logicalCol >= columns) break;

			// Fill the grid for this cell's span
			for (let r = rowIndex; r < Math.min(rowIndex + cellRowspan, rows); r++) {
				for (
					let c = logicalCol;
					c < Math.min(logicalCol + cellColspan, columns);
					c++
				) {
					if (grid[r] && c < grid[r].length) {
						grid[r][c] = { rowIndex, colIndex };
					}
				}
			}

			logicalCol += cellColspan;
		}
	}

	return grid;
}

/**
 * Converts cell array indices (rowIndex, colIndex) to logical grid position
 * Uses the logical grid to find where this cell actually starts
 */
export function cellToLogicalPosition(
	cells: TableCell[][],
	rowIndex: number,
	colIndex: number,
): { logicalRow: number; logicalCol: number } {
	// Validate input parameters
	if (
		!cells ||
		cells.length === 0 ||
		rowIndex < 0 ||
		rowIndex >= cells.length
	) {
		console.warn(
			`Invalid rowIndex ${rowIndex} for table with ${cells?.length || 0} rows`,
		);
		return { logicalRow: Math.max(0, rowIndex), logicalCol: 0 };
	}

	const row = cells[rowIndex];
	if (!row || colIndex < 0 || colIndex >= row.length) {
		console.warn(
			`Invalid colIndex ${colIndex} for row ${rowIndex} with ${row?.length || 0} columns`,
		);
		return { logicalRow: rowIndex, logicalCol: 0 };
	}

	// For row, it's straightforward - the rowIndex is the logical row
	const logicalRow = rowIndex;

	// Create a logical grid to find the correct position
	// We need to determine the table dimensions first
	let maxCols = 0;
	for (const tableRow of cells) {
		let colCount = 0;
		for (const cell of tableRow) {
			colCount += cell.colspan || 1; // Default to 1 if colspan is undefined
		}
		maxCols = Math.max(maxCols, colCount);
	}

	if (maxCols === 0) {
		return { logicalRow, logicalCol: 0 };
	}

	const grid = createLogicalGrid(cells, cells.length, maxCols);

	// Validate grid dimensions
	if (!grid || grid.length <= rowIndex || !grid[rowIndex]) {
		console.warn(
			`Grid creation failed or invalid dimensions for row ${rowIndex}`,
		);
		return { logicalRow, logicalCol: 0 };
	}

	// Find the logical position where this specific cell starts
	for (
		let logicalCol = 0;
		logicalCol < maxCols && logicalCol < grid[rowIndex].length;
		logicalCol++
	) {
		const cellRef = grid[rowIndex][logicalCol];
		if (
			cellRef &&
			cellRef.rowIndex === rowIndex &&
			cellRef.colIndex === colIndex
		) {
			return { logicalRow, logicalCol };
		}
	}

	// Fallback - should not happen if grid is correct
	console.warn(
		`Could not find logical position for cell [${rowIndex}][${colIndex}]`,
	);
	return { logicalRow, logicalCol: 0 };
}

/**
 * Expands a selection rectangle to include the full extent of any merged cells
 * that are partially selected. This ensures the selection always forms a complete
 * rectangle that doesn't partially select merged cells.
 */
export function expandSelectionForMergedCells(
	cells: TableCell[][],
	selection: {
		start: { row: number; col: number };
		end: { row: number; col: number };
	},
	rows: number,
	columns: number,
): {
	start: { row: number; col: number };
	end: { row: number; col: number };
} {
	let minLogicalRow = Math.min(selection.start.row, selection.end.row);
	let maxLogicalRow = Math.max(selection.start.row, selection.end.row);
	let minLogicalCol = Math.min(selection.start.col, selection.end.col);
	let maxLogicalCol = Math.max(selection.start.col, selection.end.col);

	// Keep expanding until no more merged cells are partially selected
	let changed = true;
	while (changed) {
		changed = false;

		// Check all cells to see if any merged cells are partially selected
		for (let rowIndex = 0; rowIndex < cells.length; rowIndex++) {
			const row = cells[rowIndex];
			let logicalCol = 0;

			for (let colIndex = 0; colIndex < row.length; colIndex++) {
				const cell = row[colIndex];
				const cellLogicalRow = rowIndex;
				const cellLogicalRowEnd = rowIndex + cell.rowspan - 1;
				const cellLogicalColEnd = logicalCol + cell.colspan - 1;

				// Check if this cell is partially selected
				const cellOverlapsSelection =
					cellLogicalRow <= maxLogicalRow &&
					cellLogicalRowEnd >= minLogicalRow &&
					logicalCol <= maxLogicalCol &&
					cellLogicalColEnd >= minLogicalCol;

				if (cellOverlapsSelection) {
					// Expand selection to include the entire cell
					if (cellLogicalRow < minLogicalRow) {
						minLogicalRow = cellLogicalRow;
						changed = true;
					}
					if (cellLogicalRowEnd > maxLogicalRow) {
						maxLogicalRow = cellLogicalRowEnd;
						changed = true;
					}
					if (logicalCol < minLogicalCol) {
						minLogicalCol = logicalCol;
						changed = true;
					}
					if (cellLogicalColEnd > maxLogicalCol) {
						maxLogicalCol = cellLogicalColEnd;
						changed = true;
					}
				}

				logicalCol += cell.colspan;
			}
		}
	}

	// Ensure bounds are within table limits
	minLogicalRow = Math.max(0, minLogicalRow);
	maxLogicalRow = Math.min(rows - 1, maxLogicalRow);
	minLogicalCol = Math.max(0, minLogicalCol);
	maxLogicalCol = Math.min(columns - 1, maxLogicalCol);

	// Preserve the original selection direction
	const originalStartRow = selection.start.row;
	const originalStartCol = selection.start.col;
	const originalEndRow = selection.end.row;
	const originalEndCol = selection.end.col;

	// Determine if selection was made from bottom-to-top or right-to-left
	const isBottomToTop = originalStartRow > originalEndRow;
	const isRightToLeft = originalStartCol > originalEndCol;

	return {
		start: {
			row: isBottomToTop ? maxLogicalRow : minLogicalRow,
			col: isRightToLeft ? maxLogicalCol : minLogicalCol,
		},
		end: {
			row: isBottomToTop ? minLogicalRow : maxLogicalRow,
			col: isRightToLeft ? minLogicalCol : maxLogicalCol,
		},
	};
}

/**
 * Checks if a selection represents exactly one physical cell, even if that cell
 * spans multiple logical grid positions (colspan/rowspan > 1).
 * This is used to distinguish between selecting a single spanned cell vs multiple cells.
 */
export function isSelectionSinglePhysicalCell(
	cells: TableCell[][],
	selection: {
		start: { row: number; col: number };
		end: { row: number; col: number };
	},
): boolean {
	if (!selection) return false;

	const minLogicalRow = Math.min(selection.start.row, selection.end.row);
	const maxLogicalRow = Math.max(selection.start.row, selection.end.row);
	const minLogicalCol = Math.min(selection.start.col, selection.end.col);
	const maxLogicalCol = Math.max(selection.start.col, selection.end.col);

	// Create logical grid to find all physical cells in the selection
	const tableRows = Math.max(...cells.map((_, idx) => idx + 1));
	const tableCols = Math.max(
		...cells.map((row) => row.reduce((sum, cell) => sum + cell.colspan, 0)),
	);
	const logicalGrid = createLogicalGrid(cells, tableRows, tableCols);

	// Track unique physical cells in the selection
	const physicalCellsInSelection = new Set<string>();

	for (let r = minLogicalRow; r <= maxLogicalRow; r++) {
		for (let c = minLogicalCol; c <= maxLogicalCol; c++) {
			const gridCell = logicalGrid[r]?.[c];
			if (gridCell) {
				const cellKey = `${gridCell.rowIndex}-${gridCell.colIndex}`;
				physicalCellsInSelection.add(cellKey);
			}
		}
	}

	// If exactly one physical cell is in the selection, check if the selection
	// exactly matches that cell's span
	if (physicalCellsInSelection.size === 1) {
		const cellKey = Array.from(physicalCellsInSelection)[0];
		const [rowIndex, colIndex] = cellKey.split("-").map(Number);

		if (rowIndex < cells.length && colIndex < cells[rowIndex].length) {
			const cell = cells[rowIndex][colIndex];

			// Find the logical position of this cell
			const { logicalRow, logicalCol } = cellToLogicalPosition(
				cells,
				rowIndex,
				colIndex,
			);

			const cellLogicalRowEnd = logicalRow + cell.rowspan - 1;
			const cellLogicalColEnd = logicalCol + cell.colspan - 1;

			// Check if selection exactly matches the cell's span
			return (
				minLogicalRow === logicalRow &&
				maxLogicalRow === cellLogicalRowEnd &&
				minLogicalCol === logicalCol &&
				maxLogicalCol === cellLogicalColEnd
			);
		}
	}

	return false;
}

/**
 * Checks if a cell at the given array indices (rowIndex, colIndex) is selected
 * based on a logical grid selection rectangle.
 */
export function isCellSelectedInLogicalGrid(
	cells: TableCell[][],
	rowIndex: number,
	colIndex: number,
	selection: {
		start: { row: number; col: number };
		end: { row: number; col: number };
	},
): boolean {
	const { logicalRow, logicalCol } = cellToLogicalPosition(
		cells,
		rowIndex,
		colIndex,
	);

	// Get the logical bounds of the selection
	const minLogicalRow = Math.min(selection.start.row, selection.end.row);
	const maxLogicalRow = Math.max(selection.start.row, selection.end.row);
	const minLogicalCol = Math.min(selection.start.col, selection.end.col);
	const maxLogicalCol = Math.max(selection.start.col, selection.end.col);

	// Check if this cell's starting position is within the selection rectangle
	return (
		logicalRow >= minLogicalRow &&
		logicalRow <= maxLogicalRow &&
		logicalCol >= minLogicalCol &&
		logicalCol <= maxLogicalCol
	);
}

/**
 * Converts logical grid position to cell array indices (rowIndex, colIndex).
 * Returns the cell that contains or starts at the given logical position.
 */
export function logicalPositionToCell(
	cells: TableCell[][],
	logicalRow: number,
	logicalCol: number,
	rows: number,
	columns: number,
): { rowIndex: number; colIndex: number } | null {
	const grid = createLogicalGrid(cells, rows, columns);

	if (
		logicalRow >= 0 &&
		logicalRow < rows &&
		logicalCol >= 0 &&
		logicalCol < columns
	) {
		const cellRef = grid[logicalRow][logicalCol];
		if (cellRef) {
			return { rowIndex: cellRef.rowIndex, colIndex: cellRef.colIndex };
		}
	}

	return null;
}

/**
 * Applies border settings to a cell side, preserving existing borders when not overridden
 */
export function applyCellBorderSide(
	cell: TableCell,
	side: "top" | "right" | "bottom" | "left",
	isLineActive: boolean,
	widthPx: number | null,
	colorHex: string | null,
	globalBorderColor: string = "#000000",
): void {
	// Initialize borderSettings if it doesn't exist
	cell.borderSettings = cell.borderSettings || {};
	const settings = cell.borderSettings;

	if (!isLineActive) {
		// If line is not active, don't modify the border - preserve existing settings
		return;
	}

	// Get current border or create new one
	const currentBorder = settings[side] || {};
	const newBorder: Partial<TableCellBorder> = {
		...currentBorder,
	};

	// Handle width changes
	if (widthPx !== null) {
		if (widthPx <= 0) {
			// Width 0 means remove the border
			delete settings[side];
			// Also set borderWidths to 0 to ensure border is disabled
			cell.borderWidths[side] = 0;
			if (Object.keys(settings).length === 0) {
				delete cell.borderSettings;
			}
			return;
		}
		newBorder.width = widthPx;
	}

	// Handle color changes
	if (colorHex !== null) {
		newBorder.color = colorHex;
	}

	// Determine the effective width for this border
	const effectiveWidth =
		newBorder.width ||
		(currentBorder as TableCellBorder)?.width ||
		cell.borderWidths[side] ||
		1;

	// If we're changing width or color, and have an effective width > 0
	if (effectiveWidth > 0 && (widthPx !== null || colorHex !== null)) {
		// Set width if it was changed
		if (widthPx !== null && widthPx > 0) {
			newBorder.width = widthPx;
		} else if (!newBorder.width) {
			// Use existing width if no new width specified
			newBorder.width = effectiveWidth;
		}

		// Ensure we have a color
		if (!newBorder.color) {
			newBorder.color =
				(currentBorder as TableCellBorder)?.color ||
				globalBorderColor ||
				"#000000";
		}

		settings[side] = newBorder as TableCellBorder;
	}

	// Clean up empty borderSettings
	if (settings && Object.keys(settings).length === 0) {
		delete cell.borderSettings;
	}
}

/**
 * Applies borders to a selection of cells using logical grid positioning
 */
export function applyBordersToSelection(
	cells: TableCell[][],
	selection: {
		start: { row: number; col: number };
		end: { row: number; col: number };
	},
	activeLines: {
		outerTop: boolean;
		outerBottom: boolean;
		outerLeft: boolean;
		outerRight: boolean;
		innerHorizontal: boolean;
		innerVertical: boolean;
	},
	widthPx: number | null,
	colorHex: string | null,
	tableRows: number,
	tableCols: number,
	globalBorderColor: string = "#000000",
): TableCell[][] {
	// Create a deep copy of cells to avoid mutations
	const newCells = cells.map((row) => row.map((cell) => ({ ...cell })));

	const R_START = Math.min(selection.start.row, selection.end.row);
	const R_END = Math.max(selection.start.row, selection.end.row);
	const C_START = Math.min(selection.start.col, selection.end.col);
	const C_END = Math.max(selection.start.col, selection.end.col);

	// Create logical grid to properly handle rowspan/colspan cells
	const logicalGrid = createLogicalGrid(newCells, tableRows, tableCols);

	// Apply borders based on logical grid positions
	for (let logicalRow = R_START; logicalRow <= R_END; logicalRow++) {
		for (let logicalCol = C_START; logicalCol <= C_END; logicalCol++) {
			const gridCell = logicalGrid[logicalRow]?.[logicalCol];
			if (!gridCell) continue;

			const { rowIndex, colIndex } = gridCell;
			const cell = newCells[rowIndex][colIndex];
			if (!cell) continue;

			// Find the actual logical position of this cell
			let cellLogicalRowStart = -1;
			let cellLogicalColStart = -1;

			// Find where this cell starts in the logical grid
			for (let r = 0; r < tableRows; r++) {
				for (let c = 0; c < tableCols; c++) {
					const gridRef = logicalGrid[r]?.[c];
					if (
						gridRef?.rowIndex === rowIndex &&
						gridRef?.colIndex === colIndex
					) {
						if (cellLogicalRowStart === -1) {
							cellLogicalRowStart = r;
							cellLogicalColStart = c;
						}
					}
				}
			}

			if (cellLogicalRowStart === -1 || cellLogicalColStart === -1) continue;

			const cellLogicalRowEnd = cellLogicalRowStart + cell.rowspan - 1;
			const cellLogicalColEnd = cellLogicalColStart + cell.colspan - 1;

			// Top border: apply if this is the top edge of the cell within selection
			const isTopEdgeOfCell = logicalRow === cellLogicalRowStart;
			const isTopEdgeOfSelection = logicalRow === R_START;
			if (isTopEdgeOfCell) {
				applyCellBorderSide(
					cell,
					"top",
					isTopEdgeOfSelection
						? activeLines.outerTop
						: activeLines.innerHorizontal,
					widthPx,
					colorHex,
					globalBorderColor,
				);
			}

			// Bottom border: apply if this is the bottom edge of the cell within selection
			const isBottomEdgeOfCell = logicalRow === cellLogicalRowEnd;
			const isBottomEdgeOfSelection = logicalRow === R_END;
			if (isBottomEdgeOfCell) {
				applyCellBorderSide(
					cell,
					"bottom",
					isBottomEdgeOfSelection
						? activeLines.outerBottom
						: activeLines.innerHorizontal,
					widthPx,
					colorHex,
					globalBorderColor,
				);
			}

			// Left border: apply if this is the left edge of the cell within selection
			const isLeftEdgeOfCell = logicalCol === cellLogicalColStart;
			const isLeftEdgeOfSelection = logicalCol === C_START;
			if (isLeftEdgeOfCell) {
				applyCellBorderSide(
					cell,
					"left",
					isLeftEdgeOfSelection
						? activeLines.outerLeft
						: activeLines.innerVertical,
					widthPx,
					colorHex,
					globalBorderColor,
				);
			}

			// Right border: apply if this is the right edge of the cell within selection
			const isRightEdgeOfCell = logicalCol === cellLogicalColEnd;
			const isRightEdgeOfSelection = logicalCol === C_END;
			if (isRightEdgeOfCell) {
				applyCellBorderSide(
					cell,
					"right",
					isRightEdgeOfSelection
						? activeLines.outerRight
						: activeLines.innerVertical,
					widthPx,
					colorHex,
					globalBorderColor,
				);
			}
		}
	}

	return newCells;
}

/**
 * Shifts borders when inserting a new row at the specified index
 * When a new row is inserted, we need to preserve border relationships:
 * - Borders between existing rows should be maintained
 * - The new row should inherit appropriate borders to maintain visual continuity
 */
export function shiftBordersForNewRow(
	cells: TableCell[][],
	insertIndex: number,
	globalBorderColor: string = "#000000",
): TableCell[][] {
	// Deep copy cells to avoid mutations
	const newCells = cells.map((row) =>
		row.map((cell) => ({
			...cell,
			borderSettings: cell.borderSettings
				? {
						top: cell.borderSettings.top
							? { ...cell.borderSettings.top }
							: undefined,
						right: cell.borderSettings.right
							? { ...cell.borderSettings.right }
							: undefined,
						bottom: cell.borderSettings.bottom
							? { ...cell.borderSettings.bottom }
							: undefined,
						left: cell.borderSettings.left
							? { ...cell.borderSettings.left }
							: undefined,
					}
				: undefined,
			borderWidths: { ...cell.borderWidths },
		})),
	);

	// If inserting at the end or beyond, no border shifting needed
	if (insertIndex >= newCells.length) {
		return newCells;
	}

	// For cells in the row that will be shifted down (originally at insertIndex)
	// we need to transfer their top borders to the row above's bottom borders
	// since there will now be a new row between them
	if (insertIndex > 0) {
		// Create logical grids to handle colspan/rowspan
		const maxCols = Math.max(
			...newCells.map((row) =>
				row.reduce((sum, cell) => sum + cell.colspan, 0),
			),
		);
		const logicalGrid = createLogicalGrid(newCells, newCells.length, maxCols);

		// For each logical column position
		for (let logicalCol = 0; logicalCol < maxCols; logicalCol++) {
			const cellAboveRef = logicalGrid[insertIndex - 1]?.[logicalCol];
			const cellBelowRef = logicalGrid[insertIndex]?.[logicalCol];

			if (cellAboveRef && cellBelowRef) {
				const cellAbove =
					newCells[cellAboveRef.rowIndex][cellAboveRef.colIndex];
				const cellBelow =
					newCells[cellBelowRef.rowIndex][cellBelowRef.colIndex];

				// If the cell below has a top border, we should preserve this relationship
				// by ensuring the cell above has an appropriate bottom border
				if (cellBelow.borderSettings?.top || cellBelow.borderWidths.top > 0) {
					const topBorder = cellBelow.borderSettings?.top;
					const topWidth = cellBelow.borderWidths.top;

					// Get the current bottom border of the cell above
					const currentBottomBorder = cellAbove.borderSettings?.bottom;
					const currentBottomWidth = cellAbove.borderWidths.bottom;

					// Use border conflict resolution to determine which border should win
					const resolvedBorder = resolveBorderConflict(
						currentBottomBorder,
						topBorder,
						globalBorderColor,
						Math.max(currentBottomWidth, topWidth),
					);

					// Apply the resolved border
					if (!cellAbove.borderSettings) {
						cellAbove.borderSettings = {
							top: undefined,
							right: undefined,
							bottom: undefined,
							left: undefined,
						};
					}
					cellAbove.borderSettings.bottom = {
						width: resolvedBorder.width,
						color: resolvedBorder.color,
					};
					cellAbove.borderWidths.bottom = resolvedBorder.width;
				}

				// If the cell above has a bottom border, preserve it
				if (
					cellAbove.borderSettings?.bottom ||
					cellAbove.borderWidths.bottom > 0
				) {
					// This border will be inherited by the new row
				}
			}
		}
	}

	return newCells;
}

/**
 * Shifts borders when inserting a new column at the specified index
 * When a new column is inserted, we need to preserve border relationships:
 * - Borders between existing columns should be maintained
 * - The new column should inherit appropriate borders to maintain visual continuity
 */
export function shiftBordersForNewColumn(
	cells: TableCell[][],
	insertIndex: number,
	globalBorderColor: string = "#000000",
): TableCell[][] {
	// Deep copy cells to avoid mutations
	const newCells = cells.map((row) =>
		row.map((cell) => ({
			...cell,
			borderSettings: cell.borderSettings
				? {
						top: cell.borderSettings.top
							? { ...cell.borderSettings.top }
							: undefined,
						right: cell.borderSettings.right
							? { ...cell.borderSettings.right }
							: undefined,
						bottom: cell.borderSettings.bottom
							? { ...cell.borderSettings.bottom }
							: undefined,
						left: cell.borderSettings.left
							? { ...cell.borderSettings.left }
							: undefined,
					}
				: undefined,
			borderWidths: { ...cell.borderWidths },
		})),
	);

	const maxCols = Math.max(
		...newCells.map((row) => row.reduce((sum, cell) => sum + cell.colspan, 0)),
	);

	// If inserting at the end or beyond, no border shifting needed
	if (insertIndex >= maxCols) {
		return newCells;
	}

	// For each row, handle border relationships across the insertion point
	for (let rowIndex = 0; rowIndex < newCells.length; rowIndex++) {
		const logicalGrid = createLogicalGrid(newCells, newCells.length, maxCols);

		// Check if there are cells on both sides of the insertion point
		let leftCellRef: { rowIndex: number; colIndex: number } | null = null;
		let rightCellRef: { rowIndex: number; colIndex: number } | null = null;

		// Find the rightmost cell before the insertion point
		for (let col = insertIndex - 1; col >= 0; col--) {
			if (logicalGrid[rowIndex]?.[col]) {
				leftCellRef = logicalGrid[rowIndex][col];
				break;
			}
		}

		// Find the leftmost cell at or after the insertion point
		for (let col = insertIndex; col < maxCols; col++) {
			if (logicalGrid[rowIndex]?.[col]) {
				rightCellRef = logicalGrid[rowIndex][col];
				break;
			}
		}

		if (leftCellRef && rightCellRef) {
			const leftCell = newCells[leftCellRef.rowIndex][leftCellRef.colIndex];
			const rightCell = newCells[rightCellRef.rowIndex][rightCellRef.colIndex];

			// If the right cell has a left border, preserve this relationship
			if (rightCell.borderSettings?.left || rightCell.borderWidths.left > 0) {
				const leftBorder = rightCell.borderSettings?.left;
				const leftWidth = rightCell.borderWidths.left;

				// Get the current right border of the left cell
				const currentRightBorder = leftCell.borderSettings?.right;
				const currentRightWidth = leftCell.borderWidths.right;

				// Use border conflict resolution to determine which border should win
				const resolvedBorder = resolveBorderConflict(
					currentRightBorder,
					leftBorder,
					globalBorderColor,
					Math.max(currentRightWidth, leftWidth),
				);

				// Apply the resolved border
				if (!leftCell.borderSettings) {
					leftCell.borderSettings = {
						top: undefined,
						right: undefined,
						bottom: undefined,
						left: undefined,
					};
				}
				leftCell.borderSettings.right = {
					width: resolvedBorder.width,
					color: resolvedBorder.color,
				};
				leftCell.borderWidths.right = resolvedBorder.width;
			}
		}
	}

	return newCells;
}

/**
 * Copies border settings from one cell side to another cell side
 * Useful for transferring borders when shifting table structure
 */
export function copyBorderSetting(
	fromCell: TableCell,
	fromSide: "top" | "right" | "bottom" | "left",
	toCell: TableCell,
	toSide: "top" | "right" | "bottom" | "left",
	globalBorderColor: string = "#000000",
): void {
	// Get the effective border from the source cell
	const sourceBorderSetting = fromCell.borderSettings?.[fromSide];
	const sourceBorderWidth = fromCell.borderWidths[fromSide];

	if (sourceBorderSetting) {
		// Copy the explicit border setting
		toCell.borderSettings = toCell.borderSettings || {};
		toCell.borderSettings[toSide] = { ...sourceBorderSetting };
		// Also copy the border width
		toCell.borderWidths[toSide] =
			sourceBorderSetting.width || sourceBorderWidth;
	} else if (sourceBorderWidth > 0) {
		// Copy the border width and use global color
		toCell.borderSettings = toCell.borderSettings || {};
		toCell.borderSettings[toSide] = {
			width: sourceBorderWidth,
			color: globalBorderColor,
		};
		// Copy the border width
		toCell.borderWidths[toSide] = sourceBorderWidth;
	}
}

/**
 * Restores borders when deleting a row at the specified index
 * When a row is deleted, we need to restore border relationships:
 * - Merge borders from the deleted row with adjacent rows
 * - Restore the original border relationships that existed before the row was inserted
 */
export function restoreBordersForDeletedRow(
	cells: TableCell[][],
	deleteIndex: number,
	globalBorderColor: string = "#000000",
): TableCell[][] {
	// Deep copy cells to avoid mutations
	const newCells = cells.map((row) =>
		row.map((cell) => ({
			...cell,
			borderSettings: cell.borderSettings
				? {
						top: cell.borderSettings.top
							? { ...cell.borderSettings.top }
							: undefined,
						right: cell.borderSettings.right
							? { ...cell.borderSettings.right }
							: undefined,
						bottom: cell.borderSettings.bottom
							? { ...cell.borderSettings.bottom }
							: undefined,
						left: cell.borderSettings.left
							? { ...cell.borderSettings.left }
							: undefined,
					}
				: undefined,
			borderWidths: { ...cell.borderWidths },
		})),
	);

	// If deleting the first or last row, or invalid index, no border merging needed
	if (deleteIndex <= 0 || deleteIndex >= newCells.length - 1) {
		return newCells;
	}

	// Create logical grid to handle colspan/rowspan
	const maxCols = Math.max(
		...newCells.map((row) => row.reduce((sum, cell) => sum + cell.colspan, 0)),
	);
	const logicalGrid = createLogicalGrid(newCells, newCells.length, maxCols);

	// For each logical column position, merge borders from deleted row
	for (let logicalCol = 0; logicalCol < maxCols; logicalCol++) {
		const cellAboveRef = logicalGrid[deleteIndex - 1]?.[logicalCol];
		const cellBelowRef = logicalGrid[deleteIndex + 1]?.[logicalCol];
		const deletedCellRef = logicalGrid[deleteIndex]?.[logicalCol];

		if (cellAboveRef && cellBelowRef && deletedCellRef) {
			const cellAbove = newCells[cellAboveRef.rowIndex][cellAboveRef.colIndex];
			const cellBelow = newCells[cellBelowRef.rowIndex][cellBelowRef.colIndex];
			const deletedCell =
				newCells[deletedCellRef.rowIndex][deletedCellRef.colIndex];

			// Merge the deleted row's top and bottom borders with adjacent rows
			// The stronger border should win
			const deletedTopBorder = deletedCell.borderSettings?.top;
			const deletedBottomBorder = deletedCell.borderSettings?.bottom;
			const deletedTopWidth = deletedCell.borderWidths.top;
			const deletedBottomWidth = deletedCell.borderWidths.bottom;

			// Merge deleted cell's top border with cell above's bottom border
			if (deletedTopBorder || deletedTopWidth > 0) {
				const currentBottomBorder = cellAbove.borderSettings?.bottom;
				const currentBottomWidth = cellAbove.borderWidths.bottom;

				const resolvedBorder = resolveBorderConflict(
					currentBottomBorder,
					deletedTopBorder,
					globalBorderColor,
					Math.max(currentBottomWidth, deletedTopWidth),
				);

				if (!cellAbove.borderSettings) {
					cellAbove.borderSettings = {
						top: undefined,
						right: undefined,
						bottom: undefined,
						left: undefined,
					};
				}
				cellAbove.borderSettings.bottom = {
					width: resolvedBorder.width,
					color: resolvedBorder.color,
				};
				cellAbove.borderWidths.bottom = resolvedBorder.width;
			}

			// Merge deleted cell's bottom border with cell below's top border
			if (deletedBottomBorder || deletedBottomWidth > 0) {
				const currentTopBorder = cellBelow.borderSettings?.top;
				const currentTopWidth = cellBelow.borderWidths.top;

				const resolvedBorder = resolveBorderConflict(
					deletedBottomBorder,
					currentTopBorder,
					globalBorderColor,
					Math.max(deletedBottomWidth, currentTopWidth),
				);

				if (!cellBelow.borderSettings) {
					cellBelow.borderSettings = {
						top: undefined,
						right: undefined,
						bottom: undefined,
						left: undefined,
					};
				}
				cellBelow.borderSettings.top = {
					width: resolvedBorder.width,
					color: resolvedBorder.color,
				};
				cellBelow.borderWidths.top = resolvedBorder.width;
			}
		}
	}

	return newCells;
}

/**
 * Restores borders when deleting a column at the specified index
 * When a column is deleted, we need to restore border relationships:
 * - Merge borders from the deleted column with adjacent columns
 * - Restore the original border relationships that existed before the column was inserted
 */
export function restoreBordersForDeletedColumn(
	cells: TableCell[][],
	deleteIndex: number,
	globalBorderColor: string = "#000000",
): TableCell[][] {
	// Deep copy cells to avoid mutations
	const newCells = cells.map((row) =>
		row.map((cell) => ({
			...cell,
			borderSettings: cell.borderSettings
				? {
						top: cell.borderSettings.top
							? { ...cell.borderSettings.top }
							: undefined,
						right: cell.borderSettings.right
							? { ...cell.borderSettings.right }
							: undefined,
						bottom: cell.borderSettings.bottom
							? { ...cell.borderSettings.bottom }
							: undefined,
						left: cell.borderSettings.left
							? { ...cell.borderSettings.left }
							: undefined,
					}
				: undefined,
			borderWidths: { ...cell.borderWidths },
		})),
	);

	const maxCols = Math.max(
		...newCells.map((row) => row.reduce((sum, cell) => sum + cell.colspan, 0)),
	);

	// If deleting the first or last column, or invalid index, no border merging needed
	if (deleteIndex <= 0 || deleteIndex >= maxCols - 1) {
		return newCells;
	}

	// For each row, handle border relationships across the deletion point
	for (let rowIndex = 0; rowIndex < newCells.length; rowIndex++) {
		const logicalGrid = createLogicalGrid(newCells, newCells.length, maxCols);

		// Find cells to the left and right of the deleted column
		let leftCellRef: { rowIndex: number; colIndex: number } | null = null;
		let rightCellRef: { rowIndex: number; colIndex: number } | null = null;
		let deletedCellRef: { rowIndex: number; colIndex: number } | null = null;

		// Find the cell to the left
		for (let col = deleteIndex - 1; col >= 0; col--) {
			if (logicalGrid[rowIndex]?.[col]) {
				leftCellRef = logicalGrid[rowIndex][col];
				break;
			}
		}

		// Find the cell to the right
		for (let col = deleteIndex + 1; col < maxCols; col++) {
			if (logicalGrid[rowIndex]?.[col]) {
				rightCellRef = logicalGrid[rowIndex][col];
				break;
			}
		}

		// Find the deleted cell
		if (logicalGrid[rowIndex]?.[deleteIndex]) {
			deletedCellRef = logicalGrid[rowIndex][deleteIndex];
		}

		if (leftCellRef && rightCellRef && deletedCellRef) {
			const leftCell = newCells[leftCellRef.rowIndex][leftCellRef.colIndex];
			const rightCell = newCells[rightCellRef.rowIndex][rightCellRef.colIndex];
			const deletedCell =
				newCells[deletedCellRef.rowIndex][deletedCellRef.colIndex];

			// Merge the deleted column's left and right borders with adjacent columns
			const deletedLeftBorder = deletedCell.borderSettings?.left;
			const deletedRightBorder = deletedCell.borderSettings?.right;
			const deletedLeftWidth = deletedCell.borderWidths.left;
			const deletedRightWidth = deletedCell.borderWidths.right;

			// Merge deleted cell's left border with left cell's right border
			if (deletedLeftBorder || deletedLeftWidth > 0) {
				const currentRightBorder = leftCell.borderSettings?.right;
				const currentRightWidth = leftCell.borderWidths.right;

				const resolvedBorder = resolveBorderConflict(
					currentRightBorder,
					deletedLeftBorder,
					globalBorderColor,
					Math.max(currentRightWidth, deletedLeftWidth),
				);

				if (!leftCell.borderSettings) {
					leftCell.borderSettings = {
						top: undefined,
						right: undefined,
						bottom: undefined,
						left: undefined,
					};
				}
				leftCell.borderSettings.right = {
					width: resolvedBorder.width,
					color: resolvedBorder.color,
				};
				leftCell.borderWidths.right = resolvedBorder.width;
			}

			// Merge deleted cell's right border with right cell's left border
			if (deletedRightBorder || deletedRightWidth > 0) {
				const currentLeftBorder = rightCell.borderSettings?.left;
				const currentLeftWidth = rightCell.borderWidths.left;

				const resolvedBorder = resolveBorderConflict(
					deletedRightBorder,
					currentLeftBorder,
					globalBorderColor,
					Math.max(deletedRightWidth, currentLeftWidth),
				);

				if (!rightCell.borderSettings) {
					rightCell.borderSettings = {
						top: undefined,
						right: undefined,
						bottom: undefined,
						left: undefined,
					};
				}
				rightCell.borderSettings.left = {
					width: resolvedBorder.width,
					color: resolvedBorder.color,
				};
				rightCell.borderWidths.left = resolvedBorder.width;
			}
		}
	}

	return newCells;
}
