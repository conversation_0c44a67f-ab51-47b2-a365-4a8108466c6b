import { saveAs } from "file-saver";
import J<PERSON><PERSON><PERSON> from "jszip";
import type { Element } from "../types/element";
import type { PageSettings } from "../types/page";
import type { PageFormat } from "./apiService";
import { storeImageFileWithFolders } from "./apiService";
import {
	generateCSS,
	generateFullDocumentHTML,
	generateSinglePageHTML,
} from "./htmlExporter";

// Log JSZip version to help diagnose issues
console.log("JSZip version:", JSZip.version || "unknown");

/**
 * Export document to a ZIP file
 * @param pages Array of page settings
 * @param elements Array of page elements
 * @param documentName Name for the exported file
 */
export async function exportToZip(
	pages: PageSettings[],
	elements: Element[],
	documentName: string = "document",
	apiFormats?: PageFormat[],
): Promise<void> {
	try {
		const zip = new JSZip();
		// Note: imageCache is local to this function and won't persist blob data across sessions.
		// const imageCache = new Map<string, Blob>(); // Removed as it wasn't effectively used for blob persistence

		// ---------------------------------------------------------------------------
		// 1) Process images so they are included in the ZIP
		// ---------------------------------------------------------------------------
		// We create a shallow clone of the elements array so we can safely mutate
		// `content` properties without affecting caller state.
		const processedElements: Element[] = elements.map((el) => ({ ...el }));

		// Keep track of images we've already added so we don't duplicate them in the ZIP
		const addedImages = new Map<string, string>(); // key: original URL, value: filename in ZIP

		for (const el of processedElements) {
			if (el.type !== "image" || !el.content) continue;

			// Skip data-URLs – they are already self-contained in HTML
			if (el.content.startsWith("data:")) continue;

			// Re-use filename if we processed the same image URL before
			if (addedImages.has(el.content)) {
				continue;
			}

			try {
				const response = await fetch(el.content);
				if (!response.ok) {
					throw new Error(`Failed to fetch image: ${response.status}`);
				}
				const blob = await response.blob();

				// Determine extension – prefer mime type, fallback to original URL
				let extension = "";
				if (blob.type) {
					const parts = blob.type.split("/");
					extension = parts[1] || "";
				}

				if (!extension) {
					const match = el.content.match(/\.([a-zA-Z0-9]+)(?:\?|#|$)/);
					extension = match ? match[1] : "png";
				}

				const filename = `image-${el.id}.${extension}`;

				// Add to ZIP under images/ directory
				zip.file(`images/${filename}`, blob);

				// Store mapping BEFORE we mutate el.content so future elements using the
				// same original URL can re-use the existing filename.
				addedImages.set(el.content, filename);
			} catch (err) {
				console.error("Failed to include image in export:", err);
				// Leave original content as-is if we cannot fetch
			}
		}

		// From this point forward always work with `processedElements`

		// Generate CSS
		const cssContent = await generateCSS();
		zip.file("styles.css", cssContent);

		// Generate main index.html using component-based rendering (with link to external CSS)
		const fullHtml = generateFullDocumentHTML(
			pages,
			processedElements,
			documentName,
			true,
			null,
			apiFormats,
		);
		zip.file("index.html", fullHtml);

		// Generate individual page HTML files
		for (let i = 0; i < pages.length; i++) {
			const page = pages[i];
			const pageNumber = i + 1;
			// Filter elements specific to this page *after* image path replacement
			const pageElements = processedElements.filter(
				(el) => el.currentPageId === page.id,
			);

			// Generate separate HTML file for each page (with link to external CSS)
			const pageHtml = generateSinglePageHTML(
				page,
				pageElements,
				pageNumber,
				documentName,
				true, // Use external CSS
				null, // No inline CSS
				apiFormats, // Pass API formats for fold marks
			);

			// Add page HTML to ZIP
			zip.file(`page-${pageNumber}.html`, pageHtml);

			// Add page JSON data (using the original page and filtered, processed elements)
			// Note: The elements here will have the updated 'content' paths for images
			const pageData = {
				page,
				elements: pageElements, // Elements for this page with updated image paths
			};
			zip.file(`page-${pageNumber}.json`, JSON.stringify(pageData, null, 2));
		}

		// Generate ZIP and download
		console.log("Generating ZIP file...");
		const content = await zip.generateAsync({ type: "blob" });
		console.log("ZIP generated, initiating download.");
		saveAs(content, `${documentName}.zip`);
	} catch (error) {
		console.error("Failed to export document:", error);
		// Consider showing an error message to the user here
		throw error; // Re-throw the error for upstream handling if necessary
	}
}

/**
 * Import document from a ZIP file
 * @param file ZIP file to import
 * @returns Promise with parsed document data
 */
export async function importFromZip(file: File): Promise<{
	pages: PageSettings[];
	elements: Element[];
}> {
	try {
		console.log(
			"Import starting for file:",
			file.name,
			"size:",
			file.size,
			"type:",
			file.type,
		);
		const zip = new JSZip();
		console.log("JSZip instance created");

		const zipContent = await zip.loadAsync(file);
		console.log(
			"ZIP content loaded successfully:",
			Object.keys(zipContent.files),
		);

		// Find all page-*.json files
		const pageJsonFiles = zipContent
			.file(/page-\d+\.json$/)
			// macOS ZIPs include duplicate files inside a __MACOSX folder and AppleDouble
			// files prefixed with "._". We ignore those here to avoid parsing the
			// duplicates which can break import logic.
			.filter(
				(file) =>
					!file.name.includes("__MACOSX") &&
					!file.name.split("/").some((part) => part.startsWith("._")),
			);
		if (pageJsonFiles.length === 0) {
			console.error(
				"Error: No page-*.json files found in ZIP. Trying fallback...",
			);

			throw new Error("Invalid document file: No page-*.json files found.");
		}
		console.log(`Found ${pageJsonFiles.length} page JSON files.`);

		const processingPromises = pageJsonFiles.map((file) => {
			const match = file.name.match(/page-(\d+)\.json$/);
			const pageNumber = match ? parseInt(match[1], 10) : -1; // Use -1 or handle error if pattern mismatch

			if (pageNumber === -1) {
				console.warn(
					`Could not extract page number from filename: ${file.name}`,
				);
				// Decide how to handle this - skip, error, default page number?
				// For now, let's return a promise that resolves to null or throws
				return Promise.resolve(null); // Or reject the promise
			}

			return file.async("text").then((jsonText) => {
				try {
					const data = JSON.parse(jsonText);
					// Basic validation
					if (
						!data ||
						typeof data.page !== "object" ||
						!Array.isArray(data.elements)
					) {
						throw new Error(
							`Invalid format in ${file.name}: missing page or elements array.`,
						);
					}
					// Ensure elements have IDs, crucial for reactivity and selection
					data.elements.forEach((el: Element, index: number) => {
						if (!el.id) {
							console.warn(
								`Element at index ${index} in ${file.name} is missing an ID. Generating one.`,
							);
							el.id = `imported-${Date.now()}-${Math.random().toString(16).substring(2, 8)}`; // Simple unique ID generation
						}
					});
					return {
						page: data.page as PageSettings,
						elements: data.elements as Element[],
						pageNumber,
					};
				} catch (parseError) {
					console.error(`Error parsing ${file.name}:`, parseError);
					throw new Error(`Failed to parse ${file.name}`); // Propagate error
				}
			});
		});

		// Wait for all files to be processed
		const results = (await Promise.all(processingPromises)).filter(
			(r) => r !== null,
		) as { page: PageSettings; elements: Element[]; pageNumber: number }[];

		// Sort results by page number
		results.sort((a, b) => a.pageNumber - b.pageNumber);

		// Aggregate pages and elements
		const allPages: PageSettings[] = [];
		const allElements: Element[] = [];
		results.forEach((result) => {
			allPages.push(result.page);
			allElements.push(...result.elements);
		});

		console.log(
			`Imported ${allPages.length} pages and ${allElements.length} elements.`,
		);

		// -------------------------------------------------------------
		// Upload any images contained in the ZIP and patch element paths
		// -------------------------------------------------------------
		try {
			// Collect image files even if the entire export is wrapped in an extra folder
			// (common when zipping in Finder on macOS). We also skip macOS resource files
			// such as __MACOSX and "._" AppleDouble entries.
			const imageFiles = Object.values(zipContent.files).filter((file) => {
				if (file.dir) return false;
				if (
					file.name.includes("__MACOSX") ||
					file.name.split("/").some((part) => part.startsWith("._")) ||
					file.name.endsWith(".DS_Store")
				) {
					return false;
				}
				// Only keep files that are inside an images/ folder (at any depth)
				if (!/(?:^|\/)images\/[^/]+$/.test(file.name)) return false;
				// Restrict to supported image extensions
				const extension = file.name.split(".").pop()?.toLowerCase();
				return extension
					? ["png", "jpg", "jpeg", "gif", "svg"].includes(extension)
					: false;
			});

			console.log(`Uploading ${imageFiles.length} images from ZIP import…`);

			for (const fileEntry of imageFiles) {
				try {
					const blob = await fileEntry.async("blob");
					const filename = fileEntry.name.split("/").pop() || "image";
					// Upload to backend (root folder)
					await storeImageFileWithFolders(blob, filename);
				} catch (err) {
					console.error(
						`Failed to upload image ${fileEntry.name} during import:`,
						err,
					);
				}
			}
		} catch (uploadErr) {
			console.error("Error processing images during import:", uploadErr);
		}

		console.log("Import completed successfully");
		return {
			pages: allPages,
			elements: allElements,
		};
	} catch (error: unknown) {
		console.error("Failed to import document:", error);
		throw error;
	}
}

/**
 * Exports the document as a ZIP file containing HTML and resources
 * @param pages Array of page settings
 * @param elements Array of all elements
 * @param documentName Name for the exported file (without extension)
 */
export async function exportDocumentAsZip(
	pages: PageSettings[],
	elements: Element[],
	documentName: string = "document",
): Promise<void> {
	try {
		// Use the main exportToZip function for consistency
		await exportToZip(pages, elements, documentName);
	} catch (error) {
		console.error("Error exporting document:", error);
		throw error;
	}
}
