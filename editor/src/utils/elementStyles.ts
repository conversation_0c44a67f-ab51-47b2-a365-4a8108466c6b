import type { Element } from "../types/element";

export interface ElementStyleOptions {
	isExportMode?: boolean;
	isSelected?: boolean;
	isEditing?: boolean;
	isLocked?: boolean;
	editingBlockId?: string | null;
	showElementBorders?: boolean;
	zIndexOffset?: number;
	elementOpacity?: number;
	tableBorderH?: number;
	tableBorderV?: number;
	tableLeftShift?: number;
	tableTopShift?: number;
}

export function calculateElementZIndex(
	element: Element,
	isSelected: boolean,
	isEditing: boolean,
	zIndexOffset: number = 0,
): number {
	if (isSelected && !isEditing) {
		return 6000; // High z-index for selected (but not actively editing) element
	} else if (isEditing) {
		return 6001; // Slightly higher if an inline editor is active
	} else {
		return (element.zIndex || 0) + zIndexOffset; // Default for unselected elements
	}
}

export function calculateElementOpacity(
	editingBlockId: string | null,
	isGenerallyInteractive: boolean,
): number {
	return editingBlockId !== null && !isGenerallyInteractive ? 0.3 : 1;
}

export function getElementInteractivity(
	element: Element,
	editingBlockId: string | null,
) {
	// Determine if the element itself should be interactive
	const isGenerallyInteractive =
		editingBlockId === null || // Interactive if no block is being edited
		element.id === editingBlockId || // Interactive if it IS the block being edited
		element.parentId === editingBlockId; // Interactive if it's a child of the block being edited

	// A block cannot be moved or resized while its contents are being edited
	const disableSelfInteractionForBlockInEditMode =
		element.type === "block" && element.id === editingBlockId;

	// Address elements should never be interactive - they're just visual containers
	const isAddressField = element.type === "address";

	return {
		isGenerallyInteractive,
		disableSelfInteractionForBlockInEditMode,
		isAddressField,
	};
}

export function getElementPointerEvents(
	isExportMode: boolean,
	isLocked: boolean,
	isGenerallyInteractive: boolean,
	isAddressField: boolean,
): "none" | "auto" {
	return isExportMode || isLocked || !isGenerallyInteractive || isAddressField
		? "none"
		: "auto";
}

export function getElementCursorStyle(
	isLocked: boolean,
	elementIsLocked: boolean,
	disableSelfInteractionForBlockInEditMode: boolean,
	isGenerallyInteractive: boolean,
	isAddressField: boolean,
): string {
	return isLocked ||
		elementIsLocked ||
		disableSelfInteractionForBlockInEditMode ||
		!isGenerallyInteractive ||
		isAddressField
		? "cursor-default"
		: "cursor-move";
}

export function getElementPositionStyles(
	element: Element,
	tableBorderH: number = 0,
	tableBorderV: number = 0,
	tableLeftShift: number = 0,
	tableTopShift: number = 0,
): React.CSSProperties {
	return {
		left: `calc(${element.x}mm - ${tableLeftShift}mm)`,
		top: `calc(${element.y}mm - ${tableTopShift}mm)`,
		width: `calc(${element.width}mm + ${tableBorderH}mm)`,
		height: `calc(${element.height}mm + ${tableBorderV}mm)`,
		transform: `rotate(${element.rotation}deg)`,
		transformOrigin: "center center",
	};
}

export function getTextElementStyles(
	verticalAlign?: "top" | "middle" | "bottom",
	isExportMode: boolean = false,
): React.CSSProperties {
	// Map vertical alignment values to flex justify-content values
	const justifyContentMap: Record<string, string> = {
		top: "flex-start",
		middle: "center",
		bottom: "flex-end",
	};

	const justifyContent = verticalAlign
		? justifyContentMap[verticalAlign]
		: "flex-start";

	const baseStyles: React.CSSProperties = {
		display: "flex",
		flexDirection: "column",
		justifyContent: justifyContent,
		height: "100%",
		verticalAlign: verticalAlign || undefined,
		overflowWrap: "break-word",
		wordWrap: "break-word",
		wordBreak: "break-word",
		maxWidth: "100%",
		whiteSpace: "pre-wrap",
		textAlign: "left", // Ensure text is left-aligned by default
	};

	if (isExportMode) {
		return {
			...baseStyles,
			border: "none", // Remove all borders in export mode
		};
	}

	return baseStyles;
}

export function getShapeWrapperStyles(element: Element): React.CSSProperties {
	return {
		width: "100%",
		height: "100%",
		overflow: "hidden",
		borderRadius: element.borderRadius
			? `${element.borderRadius[0]}mm ${element.borderRadius[1]}mm ${element.borderRadius[2]}mm ${element.borderRadius[3]}mm`
			: element.shapeType === "circle"
				? "50%"
				: "0",
		border:
			element.borderWidth && element.borderWidth > 0
				? `${element.borderWidth}pt solid ${element.borderColor || "#000000"}`
				: "none",
		boxSizing: "border-box",
	};
}

export function getImageStyles(element: Element): React.CSSProperties {
	return {
		borderRadius: element.borderRadius
			? `${element.borderRadius[0]}mm ${element.borderRadius[1]}mm ${element.borderRadius[2]}mm ${element.borderRadius[3]}mm`
			: "0",
		overflow: "hidden",
	};
}
