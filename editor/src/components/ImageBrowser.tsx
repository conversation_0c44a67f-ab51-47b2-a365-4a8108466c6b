import { Loader2, Search, Upload, X } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { FolderSidebar } from "@/components/ui/FolderSidebar";
import { ImageContextMenu } from "@/components/ui/ImageContextMenu";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useImageClipboard } from "@/hooks/useImageClipboard";
import type {
	ImageFilters,
	ImageSelection,
	ImageWithFolders,
	UploadProgress,
} from "@/types/image";
import {
	fetchAvailableImages,
	fetchImageFolders,
	storeImageFileWithFolders,
	updateImageFolders,
} from "@/utils/apiService";

interface ImageBrowserProps {
	onSelectImage: (imageUrl: string, aspectRatio: number) => void;
	onClose: () => void;
	isOpen: boolean;
	role?: string;
}

export function ImageBrowser({
	onSelectImage,
	onClose,
	isOpen,
	role,
}: ImageBrowserProps) {
	// State management
	const [images, setImages] = useState<ImageWithFolders[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [imageLoading, setImageLoading] = useState<Record<string, boolean>>({});

	// UI state
	const [filters, setFilters] = useState<ImageFilters>({
		folderId: null,
		searchTerm: "",
		showOnlySelected: false,
	});
	const [selection, setSelection] = useState<ImageSelection>({
		selectedImages: new Set(),
		lastSelected: null,
	});
	const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
	const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([]);
	const [isDragOver, setIsDragOver] = useState(false);

	// Context menu state
	const [contextMenu, setContextMenu] = useState<{
		isOpen: boolean;
		position: { x: number; y: number };
		imageUrl?: string;
	}>({
		isOpen: false,
		position: { x: 0, y: 0 },
	});

	// Refs
	const fileInputRef = useRef<HTMLInputElement>(null);
	const dropZoneRef = useRef<HTMLDivElement>(null);
	const scrollAreaRef = useRef<HTMLDivElement>(null);

	// Hooks
	const clipboard = useImageClipboard();

	// Role-based permissions
	const isAgency = role === "agency";

	// Handle image load state
	const handleImageLoad = useCallback((imageUrl: string) => {
		setImageLoading((prev) => ({ ...prev, [imageUrl]: false }));
	}, []);

	const handleImageError = useCallback((imageUrl: string) => {
		setImageLoading((prev) => ({ ...prev, [imageUrl]: false }));
		console.error("Error loading thumbnail for:", imageUrl);
	}, []);

	// Load data
	useEffect(() => {
		if (!isOpen) return;

		const loadData = async () => {
			try {
				setLoading(true);
				const [imagesData] = await Promise.all([
					fetchAvailableImages(),
					fetchImageFolders(),
				]);

				// Transform images data
				const transformedImages: ImageWithFolders[] = imagesData.map(
					(img: {
						id?: number;
						thumb: string;
						image: string;
						originalName: string;
						folders?: number[];
					}) => ({
						id: img.id, // Use actual database ID
						thumb: img.thumb,
						image: img.image,
						originalName: img.originalName,
						folders: img.folders || [],
					}),
				);

				setImages(transformedImages);

				// Initialize loading state for all images
				const loadingStates: Record<string, boolean> = {};
				transformedImages.forEach((img) => {
					loadingStates[img.image] = true;
				});
				setImageLoading(loadingStates);
			} catch (err) {
				console.error("Failed to load data:", err);
				setError("Failed to load images and folders");
			} finally {
				setLoading(false);
			}
		};

		loadData();
	}, [isOpen]);

	const refreshImages = useCallback(async () => {
		const updatedImages = await fetchAvailableImages();
		const transformed = updatedImages.map(
			(img: {
				id?: number;
				thumb: string;
				image: string;
				originalName: string;
				folders?: number[];
			}) => ({
				id: img.id,
				thumb: img.thumb,
				image: img.image,
				originalName: img.originalName,
				folders: img.folders || [],
			}),
		);
		setImages(transformed);

		// Initialize loading state for refreshed images
		const loadingStates: Record<string, boolean> = {};
		transformed.forEach((img) => {
			loadingStates[img.image] = true;
		});
		setImageLoading(loadingStates);
	}, []);

	// Filter images based on current filters
	const filteredImages = images.filter((img) => {
		// Folder filter
		if (filters.folderId !== null) {
			if (!img.folders.includes(filters.folderId)) return false;
		} else if (filters.folderId === null && img.folders.length > 0) {
			// Show only root images when no folder is selected
			return false;
		}

		// Search filter
		if (filters.searchTerm) {
			const searchLower = filters.searchTerm.toLowerCase();
			if (!img.originalName.toLowerCase().includes(searchLower)) return false;
		}

		// Selection filter
		if (filters.showOnlySelected) {
			if (!selection.selectedImages.has(img.image)) return false;
		}

		return true;
	});

	// Handle image selection
	const handleImageClick = async (
		img: ImageWithFolders,
		event: React.MouseEvent,
	) => {
		if (event.ctrlKey || event.metaKey) {
			// Multi-select with Ctrl/Cmd
			const newSelected = new Set(selection.selectedImages);
			if (newSelected.has(img.image)) {
				newSelected.delete(img.image);
			} else {
				newSelected.add(img.image);
			}
			setSelection({ selectedImages: newSelected, lastSelected: img.image });
		} else if (event.shiftKey && selection.lastSelected) {
			// Range select with Shift
			const startIndex = filteredImages.findIndex(
				(i) => i.image === selection.lastSelected,
			);
			const endIndex = filteredImages.findIndex((i) => i.image === img.image);
			const [start, end] = [
				Math.min(startIndex, endIndex),
				Math.max(startIndex, endIndex),
			];

			const newSelected = new Set(selection.selectedImages);
			for (let i = start; i <= end; i++) {
				newSelected.add(filteredImages[i].image);
			}
			setSelection({ selectedImages: newSelected, lastSelected: img.image });
		} else {
			// Single select or double-click to add to document
			if (
				selection.selectedImages.size === 1 &&
				selection.selectedImages.has(img.image)
			) {
				// Double-click to add image to document
				await handleImageSelect(img);
			} else {
				// Single select
				setSelection({
					selectedImages: new Set([img.image]),
					lastSelected: img.image,
				});
			}
		}
	};

	const handleImageSelect = async (img: ImageWithFolders) => {
		try {
			const imageElement = new Image();
			imageElement.src = img.image; // Use the full image URL for aspect ratio calculation

			await new Promise<void>((resolve, reject) => {
				imageElement.onload = () => resolve();
				imageElement.onerror = () => reject(new Error("Failed to load image"));
			});

			const aspectRatio =
				imageElement.naturalWidth / imageElement.naturalHeight;
			onSelectImage(img.image, aspectRatio);
		} catch (err) {
			console.error("Error selecting image:", err);
			// Fallback to 1:1 aspect ratio if image fails to load
			onSelectImage(img.image, 1);
		}
	};

	// Context menu handlers
	const handleRightClick = (event: React.MouseEvent, img: ImageWithFolders) => {
		event.preventDefault();
		event.stopPropagation();
		if (!selection.selectedImages.has(img.image)) {
			setSelection({
				selectedImages: new Set([img.image]),
				lastSelected: img.image,
			});
		}
		setContextMenu({
			isOpen: true,
			position: { x: event.clientX, y: event.clientY },
			imageUrl: img.image,
		});
	};

	// Handle right-click on empty space
	const handleEmptySpaceRightClick = useCallback((event: React.MouseEvent) => {
		event.preventDefault();
		event.stopPropagation();

		// Don't clear selection when right-clicking on empty space
		// Keep the current selection so copy/cut operations are available
		setContextMenu({
			isOpen: true,
			position: { x: event.clientX, y: event.clientY },
			imageUrl: undefined, // No specific image selected
		});
	}, []);

	// Keyboard shortcuts
	useEffect(() => {
		const handleKeyDown = (event: KeyboardEvent) => {
			if (!isOpen) return;

			if (event.ctrlKey || event.metaKey) {
				switch (event.key.toLowerCase()) {
					case "a":
						event.preventDefault();
						setSelection({
							selectedImages: new Set(filteredImages.map((img) => img.image)),
							lastSelected:
								filteredImages[filteredImages.length - 1]?.image || null,
						});
						break;
					case "c":
						event.preventDefault();
						if (isAgency && selection.selectedImages.size > 0) {
							const selectedImgs = images.filter((img) =>
								selection.selectedImages.has(img.image),
							);
							clipboard.copyImages(selectedImgs);
						}
						break;
					case "x":
						event.preventDefault();
						if (isAgency && selection.selectedImages.size > 0) {
							const selectedImgs = images.filter((img) =>
								selection.selectedImages.has(img.image),
							);
							clipboard.cutImages(selectedImgs);
						}
						break;
					case "v":
						event.preventDefault();
						if (isAgency && clipboard.hasClipboardData()) {
							// Trigger the paste operation (same as context menu paste)
							const clipboardData = clipboard.getClipboardData();
							if (clipboardData) {
								(async () => {
									try {
										const targetFolderIds = filters.folderId
											? [filters.folderId]
											: [];
										const imageIds = clipboardData.images
											.map((img) => img.id)
											.filter((id): id is number => id !== undefined);

										if (imageIds.length > 0) {
											await updateImageFolders({
												images: imageIds.map((imageId) => ({
													imageId,
													folderIds: targetFolderIds,
												})),
											});

											if (clipboardData.operation === "cut") {
												clipboard.clearClipboard();
											}

											await refreshImages();
										}
									} catch (error) {
										console.error("Failed to paste images:", error);
										setError("Failed to paste images");
									}
								})();
							}
						}
						break;
				}
			}

			if (event.key === "Escape") {
				if (contextMenu.isOpen) {
					setContextMenu((prev) => ({ ...prev, isOpen: false }));
				} else if (selection.selectedImages.size > 0) {
					setSelection({ selectedImages: new Set(), lastSelected: null });
				} else {
					onClose();
				}
			}
		};

		document.addEventListener("keydown", handleKeyDown);
		return () => document.removeEventListener("keydown", handleKeyDown);
	}, [
		isOpen,
		selection,
		filteredImages,
		clipboard,
		contextMenu.isOpen,
		onClose,
		images,
		isAgency,
		refreshImages,
		filters.folderId,
	]);

	// Upload handling
	const handleFileUpload = useCallback(
		async (files: FileList) => {
			const fileArray = Array.from(files);
			const newProgress: UploadProgress[] = fileArray.map((file) => ({
				fileId: `${file.name}-${Date.now()}`,
				fileName: file.name,
				progress: 0,
				status: "pending",
			}));

			setUploadProgress((prev) => [...prev, ...newProgress]);

			for (let i = 0; i < fileArray.length; i++) {
				const file = fileArray[i];
				const progressId = newProgress[i].fileId;

				try {
					setUploadProgress((prev) =>
						prev.map((p) =>
							p.fileId === progressId
								? { ...p, status: "uploading", progress: 50 }
								: p,
						),
					);

					const folderIds = filters.folderId ? [filters.folderId] : undefined;
					await storeImageFileWithFolders(file, file.name, folderIds);

					setUploadProgress((prev) =>
						prev.map((p) =>
							p.fileId === progressId
								? { ...p, status: "success", progress: 100 }
								: p,
						),
					);

					// Refresh images after successful upload
					setTimeout(refreshImages, 1000);
				} catch (error) {
					console.error("Upload error:", error);
					setUploadProgress((prev) =>
						prev.map((p) =>
							p.fileId === progressId
								? { ...p, status: "error", error: "Upload failed" }
								: p,
						),
					);
				}
			}

			// Clear progress after delay
			setTimeout(() => {
				setUploadProgress((prev) =>
					prev.filter((p) => !newProgress.some((n) => n.fileId === p.fileId)),
				);
			}, 3000);
		},
		[filters.folderId, refreshImages],
	);

	// Drag and drop handlers
	const handleDrop = useCallback(
		(event: React.DragEvent) => {
			event.preventDefault();
			setIsDragOver(false);

			// Only allow file uploads for agency users
			if (!isAgency) {
				return;
			}

			const files = event.dataTransfer.files;
			if (files.length > 0) {
				// Filter for image files only
				const imageFiles = Array.from(files).filter((file) =>
					file.type.startsWith("image/"),
				);

				if (imageFiles.length > 0) {
					handleFileUpload(imageFiles as unknown as FileList);
				}
			}
		},
		[isAgency, handleFileUpload],
	);

	const handleDragOver = useCallback(
		(event: React.DragEvent) => {
			event.preventDefault();
			// Only show drag over state for agency users
			if (isAgency) {
				setIsDragOver(true);
			}
		},
		[isAgency],
	);

	const handleDragLeave = useCallback((event: React.DragEvent) => {
		event.preventDefault();
		// Only set dragOver to false if we're leaving the drop zone entirely
		if (!event.currentTarget.contains(event.relatedTarget as Node)) {
			setIsDragOver(false);
		}
	}, []);

	// Bind events to scroll area viewport
	useEffect(() => {
		if (!isOpen || !scrollAreaRef.current) return;

		// Find the viewport element
		const viewport = scrollAreaRef.current.querySelector(
			"[data-radix-scroll-area-viewport]",
		) as HTMLElement;
		if (!viewport) return;

		const handleViewportContextMenu = (event: MouseEvent) => {
			handleEmptySpaceRightClick(event as unknown as React.MouseEvent);
		};

		const handleViewportDrop = (event: DragEvent) => {
			handleDrop(event as unknown as React.DragEvent);
		};

		const handleViewportDragOver = (event: DragEvent) => {
			handleDragOver(event as unknown as React.DragEvent);
		};

		const handleViewportDragLeave = (event: DragEvent) => {
			handleDragLeave(event as unknown as React.DragEvent);
		};

		viewport.addEventListener("contextmenu", handleViewportContextMenu);
		viewport.addEventListener("drop", handleViewportDrop);
		viewport.addEventListener("dragover", handleViewportDragOver);
		viewport.addEventListener("dragleave", handleViewportDragLeave);

		return () => {
			viewport.removeEventListener("contextmenu", handleViewportContextMenu);
			viewport.removeEventListener("drop", handleViewportDrop);
			viewport.removeEventListener("dragover", handleViewportDragOver);
			viewport.removeEventListener("dragleave", handleViewportDragLeave);
		};
	}, [
		isOpen,
		handleEmptySpaceRightClick,
		handleDrop,
		handleDragOver,
		handleDragLeave,
	]);

	if (!isOpen) return null;

	return (
		<div
			className="fixed inset-0 z-[9999] bg-background"
			data-image-browser="true"
		>
			{/* Header */}
			<div className="border-b bg-background p-4">
				<div className="flex items-center justify-between">
					<div className="flex items-center space-x-4">
						<h2 className="text-xl font-semibold">Bildverwaltung</h2>

						{/* Search */}
						<div className="relative">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
							<Input
								placeholder="Bilder suchen..."
								value={filters.searchTerm}
								onChange={(e) =>
									setFilters((prev) => ({
										...prev,
										searchTerm: e.target.value,
									}))
								}
								className="pl-10 w-64"
							/>
						</div>
					</div>

					<div className="flex items-center space-x-2">
						{/* Upload button - only visible for agency */}
						{isAgency && (
							<Button
								variant="outline"
								onClick={() => fileInputRef.current?.click()}
							>
								<Upload className="h-4 w-4 mr-2" />
								Hochladen
							</Button>
						)}

						{/* Close button */}
						<Button variant="ghost" onClick={onClose}>
							<X className="h-4 w-4" />
						</Button>
					</div>
				</div>

				{/* Upload progress */}
				{uploadProgress.length > 0 && (
					<div className="mt-4 space-y-3 p-4 bg-muted/50 rounded-lg">
						<h3 className="text-sm font-medium">Upload Status</h3>
						{uploadProgress.map((progress) => (
							<div key={progress.fileId} className="space-y-2">
								<div className="flex items-center justify-between text-sm">
									<span className="truncate max-w-xs">{progress.fileName}</span>
									<span
										className={`
                                        ${progress.status === "success" ? "text-green-600" : ""}
                                        ${progress.status === "error" ? "text-red-600" : ""}
                                        ${progress.status === "uploading" ? "text-blue-600" : ""}
                                    `}
									>
										{progress.status === "success"
											? "Erfolgreich"
											: progress.status === "error"
												? "Fehler"
												: progress.status === "uploading"
													? `${progress.progress}%`
													: "Wartet..."}
									</span>
								</div>
								<div className="w-full bg-background rounded-full h-2">
									<div
										className={`h-2 rounded-full transition-all duration-300 ${
											progress.status === "success"
												? "bg-green-600"
												: progress.status === "error"
													? "bg-red-600"
													: "bg-blue-600"
										}`}
										style={{ width: `${progress.progress}%` }}
									/>
								</div>
								{progress.status === "error" && progress.error && (
									<div className="text-xs text-red-600">{progress.error}</div>
								)}
							</div>
						))}
					</div>
				)}
			</div>

			{/* Main content */}
			<div className="flex h-[calc(100%-theme(spacing.20))]">
				{/* Sidebar */}
				<FolderSidebar
					selectedFolderId={filters.folderId}
					onFolderSelect={(folderId) =>
						setFilters((prev) => ({ ...prev, folderId }))
					}
					filters={filters}
					onFiltersChange={(newFilters) =>
						setFilters((prev) => ({ ...prev, ...newFilters }))
					}
					isCollapsed={isSidebarCollapsed}
					onToggleCollapse={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
					role={role}
				/>

				{/* Image grid */}
				<div className="flex-1 flex flex-col relative">
					{/* Global drop overlay */}
					{isDragOver && isAgency && (
						<div className="absolute inset-0 z-20 bg-primary/10 border-4 border-dashed border-primary rounded-lg flex items-center justify-center pointer-events-none">
							<div className="text-center text-primary">
								<div className="text-2xl font-bold mb-2">
									📁 Bilder hier ablegen
								</div>
								<div className="text-lg">
									Zum Upload in{" "}
									{filters.folderId
										? "den ausgewählten Ordner"
										: "den Hauptordner"}
								</div>
							</div>
						</div>
					)}

					{/* Grid header */}
					<div className="border-b p-4">
						<div className="flex items-center justify-between">
							<div className="text-sm text-muted-foreground">
								{filteredImages.length} Bilder
								{selection.selectedImages.size > 0 &&
									` • ${selection.selectedImages.size} ausgewählt`}
							</div>
						</div>
					</div>

					{/* Grid content */}
					<ScrollArea ref={scrollAreaRef} className="flex-1">
						<div
							ref={dropZoneRef}
							role="application"
							className="p-6 min-h-full"
							onDrop={handleDrop}
							onDragOver={handleDragOver}
							onDragLeave={handleDragLeave}
							onContextMenu={handleEmptySpaceRightClick}
						>
							{loading ? (
								<div
									role="application"
									className="flex justify-center items-center py-20"
									onContextMenu={handleEmptySpaceRightClick}
								>
									<Loader2 className="h-8 w-8 animate-spin" />
								</div>
							) : error ? (
								<div
									role="application"
									className="text-center py-20 text-red-500"
									onContextMenu={handleEmptySpaceRightClick}
								>
									{error}
								</div>
							) : filteredImages.length === 0 ? (
								<section
									className="text-center py-20 text-muted-foreground"
									aria-label="Empty image area"
									onContextMenu={handleEmptySpaceRightClick}
								>
									{isDragOver ? (
										<>
											<div className="text-lg font-medium text-primary mb-2">
												Bilder hier ablegen
											</div>
											<div className="text-sm">Zum Upload in diesen Ordner</div>
										</>
									) : (
										"Keine Bilder gefunden"
									)}
								</section>
							) : (
								<div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-4">
									{filteredImages.map((img) => {
										const isSelected = selection.selectedImages.has(img.image);
										const isCut = clipboard.isImageCut(img.image);

										return (
											<button
												key={img.image}
												type="button"
												className={`
                                                    relative cursor-pointer border-2 rounded-lg p-2 transition-all hover:border-primary select-none bg-transparent
                                                    ${isSelected ? "border-primary bg-primary/5" : "border-border"}
                                                    ${isCut ? "opacity-50" : ""}
                                                `}
												aria-label={`Image: ${img.originalName}`}
												onClick={(e) => handleImageClick(img, e)}
												onKeyDown={(e) => {
													if (e.key === "Enter" || e.key === " ") {
														e.preventDefault();
														handleImageClick(
															img,
															e as unknown as React.MouseEvent,
														);
													}
												}}
												onContextMenu={(e) => handleRightClick(e, img)}
												onDragStart={(e) => e.preventDefault()}
											>
												{/* Checkbox */}
												<div className="absolute top-1 left-1 z-10">
													<div
														className={`
                                                        w-4 h-4 rounded border-2 flex items-center justify-center text-xs
                                                        ${isSelected ? "bg-primary border-primary text-primary-foreground" : "bg-background border-border"}
                                                    `}
													>
														{isSelected && "✓"}
													</div>
												</div>

												{/* Image */}
												<div className="aspect-square bg-muted rounded mb-2 overflow-hidden select-none">
													{imageLoading[img.image] && (
														<div className="absolute inset-0 flex items-center justify-center">
															<Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
														</div>
													)}
													<img
														src={img.thumb}
														alt={img.originalName}
														className="w-full h-full object-contain select-none pointer-events-none"
														draggable={false}
														onLoad={() => handleImageLoad(img.image)}
														onError={() => handleImageError(img.image)}
														style={{
															opacity: imageLoading[img.image] ? 0 : 1,
															transition: "opacity 0.2s ease-in-out",
														}}
													/>
												</div>

												{/* Name */}
												<p className="text-xs truncate select-none pointer-events-none">
													{img.originalName}
												</p>
											</button>
										);
									})}
								</div>
							)}
						</div>
					</ScrollArea>
				</div>
			</div>

			{/* Hidden file input - only for agency users */}
			{isAgency && (
				<input
					ref={fileInputRef}
					type="file"
					multiple
					accept="image/*"
					className="hidden"
					onChange={(e) => {
						if (e.target.files) {
							handleFileUpload(e.target.files);
							e.target.value = "";
						}
					}}
				/>
			)}

			{/* Context menu */}
			<ImageContextMenu
				isOpen={contextMenu.isOpen}
				position={contextMenu.position}
				onClose={() => setContextMenu((prev) => ({ ...prev, isOpen: false }))}
				selectedCount={selection.selectedImages.size}
				canPaste={clipboard.hasClipboardData()}
				role={role}
				onCopy={() => {
					if (!isAgency) return;
					const selectedImgs = images.filter((img) =>
						selection.selectedImages.has(img.image),
					);
					clipboard.copyImages(selectedImgs);
				}}
				onCut={() => {
					if (!isAgency) return;
					const selectedImgs = images.filter((img) =>
						selection.selectedImages.has(img.image),
					);
					clipboard.cutImages(selectedImgs);
				}}
				onPaste={async () => {
					if (!isAgency) return;
					const clipboardData = clipboard.getClipboardData();
					if (!clipboardData) return;

					try {
						// Get target folder IDs - if no folder selected, move to root (empty array)
						const targetFolderIds = filters.folderId ? [filters.folderId] : [];

						// Extract image IDs from clipboard
						const imageIds = clipboardData.images
							.map((img) => img.id)
							.filter((id): id is number => id !== undefined);

						if (imageIds.length === 0) {
							setError("No valid images to paste");
							return;
						}

						// Update folder assignments
						await updateImageFolders({
							images: imageIds.map((imageId) => ({
								imageId,
								folderIds: targetFolderIds,
							})),
						});

						// Clear clipboard if it was a cut operation
						if (clipboardData.operation === "cut") {
							clipboard.clearClipboard();
						}

						// Refresh images
						await refreshImages();
					} catch (error) {
						console.error("Failed to paste images:", error);
						setError("Failed to paste images");
					}
				}}
				onSelect={() => {
					if (
						contextMenu.imageUrl &&
						selection.selectedImages.has(contextMenu.imageUrl)
					) {
						const img = images.find((i) => i.image === contextMenu.imageUrl);
						if (img) handleImageSelect(img);
					}
				}}
			/>
		</div>
	);
}
