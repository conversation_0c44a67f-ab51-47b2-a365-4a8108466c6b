import type { Mark } from "@tiptap/pm/model";
import type { Editor } from "@tiptap/react";
import { useCallback, useRef } from "react";
import {
	applyDefaultStyles,
	createStoredMarks,
	DEFAULT_FONT_COLOR,
	DEFAULT_LINE_HEIGHT,
	extractFontFamily,
	isEmptyContent,
} from "../utils/editorUtils";

interface UseEditorStyleHandlersProps {
	defaultFontSize: string;
	initialVerticalAlign?: string | null;
	setIsTextEditorFocused: (focused: boolean) => void;
	onBlurCallback?: () => void;
	onChange: (content: string) => void;
}

export const useEditorStyleHandlers = ({
	defaultFontSize,
	initialVerticalAlign,
	setIsTextEditorFocused,
	onBlurCallback,
	onChange,
}: UseEditorStyleHandlersProps) => {
	const prevContentRef = useRef<string>("");

	const onFocus = useCallback(
		({ editor }: { editor: Editor }) => {
			setIsTextEditorFocused(true);

			const html = editor.getHTML();
			const currentTextStyle = editor.getAttributes("textStyle");
			const currentColor = editor.getAttributes("color");

			// Build textStyle mark with all required attributes
			const textStyleAttrs: Record<string, string> = {};
			textStyleAttrs.fontSize = currentTextStyle?.fontSize || defaultFontSize;
			textStyleAttrs.lineHeight =
				currentTextStyle?.lineHeight || DEFAULT_LINE_HEIGHT;

			const existingFontFamily =
				extractFontFamily(html) || currentTextStyle?.fontFamily;
			if (existingFontFamily) {
				textStyleAttrs.fontFamily = existingFontFamily;
			}

			if (initialVerticalAlign) {
				textStyleAttrs.verticalAlign = initialVerticalAlign;
			}

			const storedMarks: Mark[] = [
				editor.schema.marks.textStyle.create(textStyleAttrs),
				editor.schema.marks.color.create({
					color: currentColor?.color || DEFAULT_FONT_COLOR,
				}),
			];

			editor.view.dispatch(editor.state.tr.setStoredMarks(storedMarks));
			applyDefaultStyles(editor, defaultFontSize, true);
		},
		[defaultFontSize, initialVerticalAlign, setIsTextEditorFocused],
	);

	const onBlur = useCallback(() => {
		setIsTextEditorFocused(false);
		onBlurCallback?.();
	}, [setIsTextEditorFocused, onBlurCallback]);

	const onCreate = useCallback(
		({ editor }: { editor: Editor }, content: string) => {
			const currentContent = content || "<p></p>";
			const hasFontSize = currentContent.includes("font-size:");
			const hasFontColor = currentContent.includes("color:");
			const hasLineHeight = currentContent.includes("line-height:");
			const isEmpty = isEmptyContent(currentContent);

			if (isEmpty && defaultFontSize && !hasFontSize) {
				const styledEmptyContent = `<p style="font-size: ${defaultFontSize}; color: ${DEFAULT_FONT_COLOR}; line-height: ${DEFAULT_LINE_HEIGHT};"></p>`;
				editor.commands.setContent(styledEmptyContent, false);
			} else if (!hasFontSize && currentContent.trim() !== "") {
				editor
					.chain()
					.focus()
					.selectAll()
					.setMark("textStyle", { fontSize: defaultFontSize })
					.run();
				editor.commands.setTextSelection(0);
			}

			if (!hasFontColor) {
				editor.chain().focus().selectAll().setColor(DEFAULT_FONT_COLOR).run();
				editor.commands.setTextSelection(0);
			}

			if (!hasLineHeight) {
				editor
					.chain()
					.focus()
					.selectAll()
					.setMark("textStyle", { lineHeight: DEFAULT_LINE_HEIGHT })
					.run();
				editor.commands.setTextSelection(0);
			}

			// Handle vertical alignment
			const verticalAlignToApply = initialVerticalAlign;
			if (verticalAlignToApply) {
				editor
					.chain()
					.focus()
					.selectAll()
					.setMark("textStyle", { verticalAlign: verticalAlignToApply })
					.run();
				editor.commands.setTextSelection(0);
			}

			// Set stored marks for new text input
			const existingFontFamily = extractFontFamily(currentContent);
			const storedMarks = createStoredMarks(
				editor,
				defaultFontSize,
				verticalAlignToApply,
				existingFontFamily,
			);
			editor.view.dispatch(editor.state.tr.setStoredMarks(storedMarks));

			prevContentRef.current = editor.getHTML();
		},
		[defaultFontSize, initialVerticalAlign],
	);

	const onUpdate = useCallback(
		({ editor }: { editor: Editor }) => {
			const html = editor.getHTML();
			const wasEmpty = isEmptyContent(prevContentRef.current);
			const isNowEmpty = isEmptyContent(html);

			// Handle content becoming empty
			if (!wasEmpty && isNowEmpty) {
				const previousFontFamily = extractFontFamily(prevContentRef.current);
				const styledEmptyContent = `<p style="font-family: ${previousFontFamily}; font-size: ${defaultFontSize}; color: ${DEFAULT_FONT_COLOR}; line-height: ${DEFAULT_LINE_HEIGHT};"></p>`;
				editor.commands.setContent(styledEmptyContent, false);

				const marks = createStoredMarks(
					editor,
					defaultFontSize,
					null,
					previousFontFamily,
				);
				editor.commands.setTextSelection(1);
				editor.view.dispatch(editor.state.tr.setStoredMarks(marks));
			}

			// Handle styling fixes for new content
			const needsStylingFix =
				(wasEmpty && !isNowEmpty) ||
				(!isNowEmpty &&
					(!html.includes("line-height:") ||
						!html.includes("color:") ||
						!html.includes("font-size:")));

			if (needsStylingFix && !isNowEmpty) {
				const selection = editor.state.selection;
				const { from } = selection;

				const existingFontFamily =
					extractFontFamily(html) || extractFontFamily(prevContentRef.current);
				const textStyleMarkAttrs: Record<string, string> = {
					fontSize: defaultFontSize,
					lineHeight: DEFAULT_LINE_HEIGHT,
				};

				if (existingFontFamily) {
					textStyleMarkAttrs.fontFamily = existingFontFamily;
				}

				editor
					.chain()
					.focus()
					.selectAll()
					.setMark("textStyle", textStyleMarkAttrs)
					.setColor(DEFAULT_FONT_COLOR)
					.setTextSelection(Math.min(from, editor.state.doc.content.size - 1))
					.run();

				const marks = createStoredMarks(
					editor,
					defaultFontSize,
					null,
					existingFontFamily,
				);
				editor.view.dispatch(editor.state.tr.setStoredMarks(marks));
			}

			prevContentRef.current = html;
			onChange(html);
		},
		[defaultFontSize, onChange],
	);

	const onSelectionUpdate = useCallback(
		({ editor }: { editor: Editor }) => {
			const html = editor.getHTML();
			const isEmpty = isEmptyContent(html);

			if (isEmpty) {
				const currentMarks = editor.getAttributes("textStyle");
				const existingFontFamily = extractFontFamily(html);
				const needsUpdate =
					!currentMarks?.fontSize ||
					!currentMarks?.lineHeight ||
					!editor.getAttributes("color")?.color ||
					(existingFontFamily && !currentMarks?.fontFamily);

				if (needsUpdate) {
					const marks = createStoredMarks(
						editor,
						defaultFontSize,
						null,
						existingFontFamily,
					);
					editor.view.dispatch(editor.state.tr.setStoredMarks(marks));
				}
			}
		},
		[defaultFontSize],
	);

	return {
		onFocus,
		onBlur,
		onCreate,
		onUpdate,
		onSelectionUpdate,
	};
};
