import { Color } from "@tiptap/extension-color";
import Subscript from "@tiptap/extension-subscript";
import Superscript from "@tiptap/extension-superscript";
import TextAlign from "@tiptap/extension-text-align";
import { TextStyle } from "@tiptap/extension-text-style";
import Underline from "@tiptap/extension-underline";
import StarterKit from "@tiptap/starter-kit";
import {
	CustomParagraph,
	CustomTextStyle,
	ParagraphCommands,
} from "../extensions";

export const useEditorConfig = () => {
	const extensions = [
		StarterKit.configure({
			paragraph: false, // Use CustomParagraph instead
			orderedList: false,
			bulletList: false,
			listItem: false,
			history: {},
		}),
		CustomParagraph,
		ParagraphCommands,
		Underline,
		TextAlign.configure({
			types: ["paragraph", "heading"],
			alignments: ["left", "center", "right", "justify"],
		}),
		TextStyle,
		CustomTextStyle,
		Color,
		Superscript,
		Subscript,
	];

	const parseOptions = {
		preserveWhitespace: "full" as const,
	};

	return {
		extensions,
		parseOptions,
	};
};
