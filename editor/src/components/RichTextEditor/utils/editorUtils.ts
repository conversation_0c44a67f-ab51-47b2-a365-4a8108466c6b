import type { Mark } from "@tiptap/pm/model";
import type { Editor } from "@tiptap/react";

// Constants for default values
export const DEFAULT_FONT_COLOR = "rgb(7, 30, 127)";
export const DEFAULT_LINE_HEIGHT = "1.2";

// Helper function to check if content is empty
export const isEmptyContent = (html: string): boolean => {
	return (
		html === "<p></p>" ||
		html === "<p><br></p>" ||
		html.includes('<br class="ProseMirror-trailingBreak">')
	);
};

// Helper function to detect vertical alignment in content
export const detectVerticalAlign = (htmlContent: string): string | null => {
	// Check for both style attribute and class-based alignment
	const styleMatch = htmlContent.match(/vertical-align:\s*(top|middle|bottom)/);
	const classMatch = htmlContent.match(/vertical-align-(top|middle|bottom)/);
	return styleMatch ? styleMatch[1] : classMatch ? classMatch[1] : null;
};

// Helper function to extract font-family from HTML content
export const extractFontFamily = (htmlContent: string): string | null => {
	const fontFamilyMatch = htmlContent.match(/font-family:\s*([^;"]+)/);
	return fontFamilyMatch ? fontFamilyMatch[1].trim() : null;
};

// Helper function to apply default styles to editor
export const applyDefaultStyles = (
	editor: Editor,
	defaultFontSize: string,
	preserveSelection = false,
) => {
	const currentTextStyle = editor.getAttributes("textStyle");
	const currentColor = editor.getAttributes("color");
	const html = editor.getHTML();

	// Build the complete style object with all missing defaults
	const styleUpdates: Record<string, string> = {};

	if (!currentTextStyle?.fontSize) {
		styleUpdates.fontSize = defaultFontSize;
	}
	if (!currentTextStyle?.lineHeight) {
		styleUpdates.lineHeight = DEFAULT_LINE_HEIGHT;
	}

	// Preserve existing font-family from HTML content or editor attributes
	const existingFontFamily =
		extractFontFamily(html) || currentTextStyle?.fontFamily;
	if (existingFontFamily) {
		styleUpdates.fontFamily = existingFontFamily;
	}

	// Apply textStyle attributes
	if (Object.keys(styleUpdates).length > 0) {
		editor.chain().setMark("textStyle", styleUpdates).run();
	}

	// Apply color separately (it's handled differently by TipTap)
	if (!currentColor?.color) {
		editor.chain().setColor(DEFAULT_FONT_COLOR).run();
	}

	if (!preserveSelection) {
		editor.commands.setTextSelection(0);
	}
};

// Helper function to apply vertical alignment to editor element
export const applyVerticalAlignToEditor = (
	editorRef: React.RefObject<HTMLDivElement | null>,
	verticalAlign: string | null,
) => {
	if (!editorRef.current) return;

	const editorElement = editorRef.current.querySelector(".ProseMirror");
	if (!editorElement) return;

	const alignmentClasses = [
		"vertical-align-top",
		"vertical-align-middle",
		"vertical-align-bottom",
	];

	if (verticalAlign) {
		const justifyContent =
			{
				top: "flex-start",
				middle: "center",
				bottom: "flex-end",
			}[verticalAlign] || "flex-start";

		editorElement.setAttribute(
			"style",
			`vertical-align: ${verticalAlign}; display: flex; flex-direction: column; height: 100%; justify-content: ${justifyContent}`,
		);

		editorElement.classList.remove(...alignmentClasses);
		editorElement.classList.add(`vertical-align-${verticalAlign}`);
	} else {
		// Remove alignment styles but keep other styles
		const currentStyle = editorElement.getAttribute("style") || "";
		const newStyle = currentStyle
			.replace(/vertical-align:\s*[^;]+;?/, "")
			.replace(/justify-content:\s*[^;]+;?/, "")
			.trim();

		if (newStyle) {
			editorElement.setAttribute("style", newStyle);
		} else {
			editorElement.setAttribute(
				"style",
				"display: flex; flex-direction: column; height: 100%;",
			);
		}

		editorElement.classList.remove(...alignmentClasses);
	}
};

// Helper function to create stored marks for consistent text styling
export const createStoredMarks = (
	editor: Editor,
	defaultFontSize: string,
	verticalAlign?: string | null,
	existingFontFamily?: string | null,
): Mark[] => {
	const textStyleAttrs: Record<string, string> = {
		fontSize: defaultFontSize,
		lineHeight: DEFAULT_LINE_HEIGHT,
	};

	if (existingFontFamily) {
		textStyleAttrs.fontFamily = existingFontFamily;
	}
	if (verticalAlign) {
		textStyleAttrs.verticalAlign = verticalAlign;
	}

	return [
		editor.schema.marks.textStyle.create(textStyleAttrs),
		editor.schema.marks.color.create({ color: DEFAULT_FONT_COLOR }),
	];
};
