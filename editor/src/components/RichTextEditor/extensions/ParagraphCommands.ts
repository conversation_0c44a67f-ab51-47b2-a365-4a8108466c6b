import { type CommandProps, Extension } from "@tiptap/core";
import type { Node as PMNode } from "@tiptap/pm/model";

// Extension to register paragraph formatting commands globally
export const ParagraphCommands = Extension.create({
	name: "paragraphCommands",

	addCommands() {
		return {
			increaseIndent:
				() =>
				({ state, dispatch }: CommandProps) => {
					const { from, to } = state.selection;
					let tr = state.tr;
					let madeChange = false;

					state.doc.nodesBetween(from, to, (node, pos) => {
						if (node.type.name === "paragraph") {
							// Ensure the node is within the actual selection range
							const nodeStart = pos;
							const nodeEnd = pos + node.nodeSize;
							// Only process if the node is fully or partially selected
							if (
								Math.max(from, nodeStart) < Math.min(to, nodeEnd) ||
								(from === to && from > nodeStart && from < nodeEnd)
							) {
								const currentIndent = node.attrs.indent || 0;
								const newIndent = currentIndent + 1;

								tr = tr.setNodeMarkup(pos, undefined, {
									...node.attrs,
									indent: newIndent,
								});
								madeChange = true;
							}
						}
						return node.isBlock;
					});

					if (madeChange && dispatch) {
						dispatch(tr.scrollIntoView());
						return true;
					}
					return false;
				},
			decreaseIndent:
				() =>
				({ state, dispatch }: CommandProps) => {
					const { from, to } = state.selection;
					let tr = state.tr;
					let madeChange = false;

					state.doc.nodesBetween(from, to, (node, pos) => {
						if (node.type.name === "paragraph") {
							const nodeStart = pos;
							const nodeEnd = pos + node.nodeSize;
							if (
								Math.max(from, nodeStart) < Math.min(to, nodeEnd) ||
								(from === to && from > nodeStart && from < nodeEnd)
							) {
								const currentIndent = node.attrs.indent || 0;
								const newIndent = Math.max(currentIndent - 1, 0);

								if (currentIndent !== newIndent) {
									tr = tr.setNodeMarkup(pos, undefined, {
										...node.attrs,
										indent: newIndent,
									});
									madeChange = true;
								}
							}
						}
						return node.isBlock;
					});

					if (madeChange && dispatch) {
						dispatch(tr.scrollIntoView());
						return true;
					}
					return false;
				},
			toggleBulletList:
				() =>
				({ state, dispatch }: CommandProps) => {
					const { from, to } = state.selection;
					let tr = state.tr;
					let madeChange = false;
					const targets: { pos: number; node: PMNode }[] = [];
					let totalSelected = 0;
					let bulletCount = 0;
					const bulletPrefix = "• ";
					const numberRegex = /^(\d+\.\s)/;

					// 1. Identify targets and count current state
					state.doc.nodesBetween(from, to, (node, pos) => {
						if (node.type.name === "paragraph") {
							const paragraphEndPos = pos + node.nodeSize;
							const overlaps =
								(pos < to && paragraphEndPos > from) ||
								(from === to && pos < from && paragraphEndPos > from);
							if (overlaps) {
								targets.push({ pos, node });
								totalSelected++;
								if (node.textContent.startsWith(bulletPrefix)) {
									bulletCount++;
								}
							}
						}
						return node.isBlock;
					});

					if (targets.length === 0) return false;

					// 2. Determine final action for the whole selection
					const finalAction = bulletCount === totalSelected ? "remove" : "add";

					// 3. Apply changes in reverse order based on finalAction
					targets.reverse().forEach((target) => {
						const { pos, node } = target;
						const paragraphStartPos = pos + 1;
						const currentText = node.textContent;
						const isCurrentlyBullet = currentText.startsWith(bulletPrefix);
						const numberMatch = currentText.match(numberRegex);

						if (finalAction === "remove") {
							if (isCurrentlyBullet) {
								tr = tr.delete(
									paragraphStartPos,
									paragraphStartPos + bulletPrefix.length,
								);
								madeChange = true;
							}
						} else {
							// finalAction === 'add'
							if (numberMatch) {
								// Remove existing number prefix first
								tr = tr.delete(
									paragraphStartPos,
									paragraphStartPos + numberMatch[1].length,
								);
							}
							if (!isCurrentlyBullet) {
								// Add bullet prefix if it doesn't exist
								tr = tr.insertText(bulletPrefix, paragraphStartPos);
								madeChange = true;
							}
						}
					});

					// 4. Dispatch transaction
					if (madeChange && dispatch) {
						dispatch(tr.scrollIntoView());
						return true;
					}
					return false;
				},
			toggleNumberedList:
				() =>
				({ state, dispatch }: CommandProps) => {
					const { from, to } = state.selection;
					let tr = state.tr;
					let madeChange = false;
					const targets: { pos: number; node: PMNode; finalNumber?: number }[] =
						[];
					let totalSelected = 0;
					let numberedCount = 0;
					const numberRegex = /^(\d+\.\s)/;
					const bulletPrefix = "• ";

					// 1. Identify targets and count current state
					state.doc.nodesBetween(from, to, (node, pos) => {
						if (node.type.name === "paragraph") {
							const paragraphEndPos = pos + node.nodeSize;
							const overlaps =
								(pos < to && paragraphEndPos > from) ||
								(from === to && pos < from && paragraphEndPos > from);
							if (overlaps) {
								targets.push({ pos, node });
								totalSelected++;
								if (node.textContent.match(numberRegex)) {
									numberedCount++;
								}
							}
						}
						return node.isBlock;
					});

					if (targets.length === 0) return false;

					// 2. Determine final action for the whole selection
					const finalAction =
						numberedCount === totalSelected ? "remove" : "add";

					// 3. Pre-calculate numbers if adding
					if (finalAction === "add") {
						let listCounter = 1;
						targets.forEach((target) => {
							target.finalNumber = listCounter++;
						});
					}

					// 4. Apply changes in reverse order based on finalAction
					targets.reverse().forEach((target) => {
						const { pos, node, finalNumber } = target;
						const paragraphStartPos = pos + 1;
						const currentText = node.textContent;
						const currentNumberMatch = currentText.match(numberRegex);
						const isCurrentlyBullet = currentText.startsWith(bulletPrefix);

						if (finalAction === "remove") {
							if (currentNumberMatch) {
								tr = tr.delete(
									paragraphStartPos,
									paragraphStartPos + currentNumberMatch[1].length,
								);
								madeChange = true;
							}
						} else {
							// finalAction === 'add'
							const newNumberPrefix = `${finalNumber}. `;
							let needsInsert = false;

							// Remove bullet if present
							if (isCurrentlyBullet) {
								tr = tr.delete(
									paragraphStartPos,
									paragraphStartPos + bulletPrefix.length,
								);
								needsInsert = true;
								madeChange = true;
							}
							// Remove existing number if different
							if (currentNumberMatch) {
								if (currentNumberMatch[0] !== newNumberPrefix) {
									tr = tr.delete(
										paragraphStartPos,
										paragraphStartPos + currentNumberMatch[1].length,
									);
									needsInsert = true;
									madeChange = true;
								} else {
									needsInsert = isCurrentlyBullet;
								}
							} else if (!isCurrentlyBullet) {
								needsInsert = true;
							}

							// Insert the new number prefix if determined necessary
							if (needsInsert) {
								tr = tr.insertText(newNumberPrefix, paragraphStartPos);
								madeChange = true;
							}
						}
					});

					// 5. Dispatch transaction
					if (madeChange && dispatch) {
						dispatch(tr.scrollIntoView());
						return true;
					}
					return false;
				},
		};
	},

	addKeyboardShortcuts() {
		return {
			Tab: () => this.editor.commands.increaseIndent(),
			"Shift-Tab": () => this.editor.commands.decreaseIndent(),

			Enter: () => {
				const { state, view } = this.editor;
				if (!view) return false;
				const { dispatch } = view;
				const { $from } = state.selection;
				const node = $from.node($from.depth);

				if (!node || node.type.name !== "paragraph") return false;

				const text = node.textContent;
				const bulletPrefix = "• ";
				const numberRegex = /^(\d+)\.\s/;
				const numberMatch = text.match(numberRegex);
				const isBulletLine = text.startsWith(bulletPrefix);
				const isNumberedLine = !!numberMatch;
				const isEmptyListItem =
					(isBulletLine && text === bulletPrefix) ||
					(isNumberedLine && text === numberMatch[0]);

				// Handle Enter on empty list item -> Untoggle list on the current line
				if (isEmptyListItem) {
					const paragraphStartPos = $from.before($from.depth) + 1;
					const prefixLength = isBulletLine
						? bulletPrefix.length
						: numberMatch
							? numberMatch[0].length
							: 0;

					if (prefixLength > 0 && dispatch) {
						const marks = state.storedMarks || $from.marks();
						let tr = state.tr;
						tr = tr.delete(paragraphStartPos, paragraphStartPos + prefixLength);
						tr = tr.setStoredMarks(marks);
						dispatch(tr);
						return true;
					}
					return false;
				}

				// Handle standard list continuation
				if (isBulletLine) {
					return this.editor
						.chain()
						.focus()
						.splitBlock({ keepMarks: true })
						.insertContent(bulletPrefix)
						.run();
				}

				if (numberMatch) {
					const currentNumber = parseInt(numberMatch[1], 10);
					const nextNumber = currentNumber + 1;
					const nextNumberPrefix = `${nextNumber}. `;
					return this.editor
						.chain()
						.focus()
						.splitBlock({ keepMarks: true })
						.insertContent(nextNumberPrefix)
						.run();
				}

				return false;
			},

			Backspace: () => {
				const { state } = this.editor;
				const { selection } = state;
				const { $from, empty } = selection;

				if (!empty) return false;

				const node = $from.node($from.depth);
				if (!node || node.type.name !== "paragraph") return false;

				const textBeforeCursor = node.textContent.substring(
					0,
					$from.parentOffset,
				);
				const bulletPrefix = "• ";
				const numberRegex = /^(\d+)\.\s/;
				const numberMatch = textBeforeCursor.match(numberRegex);

				// Case 1: Backspace right after the prefix -> Untoggle list
				if (textBeforeCursor === bulletPrefix) {
					return this.editor.commands.toggleBulletList();
				}
				if (numberMatch && textBeforeCursor === numberMatch[0]) {
					return this.editor.commands.toggleNumberedList();
				}

				// Case 2: Backspace at the very start of an indented line -> Outdent
				if ($from.parentOffset === 0 && (node.attrs.indent || 0) > 0) {
					return this.editor.commands.decreaseIndent();
				}

				return false;
			},
		};
	},
});

// Declare the commands for TypeScript
declare module "@tiptap/core" {
	interface Commands<ReturnType> {
		paragraphCommands: {
			increaseIndent: () => ReturnType;
			decreaseIndent: () => ReturnType;
			toggleBulletList: () => ReturnType;
			toggleNumberedList: () => ReturnType;
		};
	}
}
