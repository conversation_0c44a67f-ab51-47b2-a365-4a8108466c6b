import { Extension } from "@tiptap/core";

// Create a custom extension to handle font family and size
export const CustomTextStyle = Extension.create({
	name: "customTextStyle",

	addGlobalAttributes() {
		return [
			{
				types: ["textStyle"],
				attributes: {
					fontFamily: {
						default: null,
						parseHTML: (element) => element.style.fontFamily,
						renderHTML: (attributes) => {
							if (!attributes.fontFamily) {
								return {};
							}
							return {
								style: `font-family: ${attributes.fontFamily}`,
							};
						},
					},
					fontSize: {
						default: null,
						parseHTML: (element) => element.style.fontSize,
						renderHTML: (attributes) => {
							if (!attributes.fontSize) {
								return {};
							}
							return {
								style: `font-size: ${attributes.fontSize}`,
							};
						},
					},
					verticalAlign: {
						default: null,
						parseHTML: (element) => element.style.verticalAlign,
						renderHTML: (attributes) => {
							if (!attributes.verticalAlign) {
								return {};
							}
							return {
								style: `vertical-align: ${attributes.verticalAlign}`,
								class: `vertical-align-${attributes.verticalAlign}`,
							};
						},
					},
					lineHeight: {
						default: null,
						parseHTML: (element) => element.style.lineHeight,
						renderHTML: (attributes) => {
							if (!attributes.lineHeight) {
								return {};
							}
							return {
								style: `line-height: ${attributes.lineHeight}`,
							};
						},
					},
					letterSpacing: {
						default: null,
						parseHTML: (element) => element.style.letterSpacing,
						renderHTML: (attributes) => {
							if (!attributes.letterSpacing) {
								return {};
							}
							return {
								style: `letter-spacing: ${attributes.letterSpacing}`,
							};
						},
					},
				},
			},
		];
	},
});
