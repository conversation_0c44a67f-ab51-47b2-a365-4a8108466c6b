import { InputRule } from "@tiptap/core";
import Paragraph, { type ParagraphOptions } from "@tiptap/extension-paragraph";

// Define options for the custom paragraph extension
interface CustomParagraphOptions extends ParagraphOptions {
	maxIndentLevel: number;
}

// Custom Paragraph extension that includes indent attributes
export const CustomParagraph = Paragraph.extend<CustomParagraphOptions>({
	name: "paragraph", // Keep the name 'paragraph' for compatibility

	addAttributes() {
		return {
			...this.parent?.(),
			indent: {
				default: 0,
				renderHTML: (attributes) => {
					if (!attributes.indent || attributes.indent === 0) {
						return {};
					}
					return {
						style: `padding-left: ${attributes.indent * 0.75}em;`,
						class: `indent-${attributes.indent}`,
					};
				},
				parseHTML: (element) => {
					const indentMatch = element
						.getAttribute("class")
						?.match(/indent-(\d+)/);
					return indentMatch ? parseInt(indentMatch[1], 10) : 0;
				},
			},
		};
	},

	// Add input rules for auto-creating lists
	addInputRules() {
		return [
			// Bullet list rule: Start line with * or - followed by space
			new InputRule({
				find: /^\s*([-*])\s$/,
				handler: ({ range, commands }) => {
					commands.deleteRange(range);
					commands.insertContent("• ");
				},
			}),
			// Numbered list rule: Start line with number+dot+space (e.g., 1. )
			new InputRule({
				find: /^\s*(\d+)\.\s$/,
				handler: ({ range, match, commands }) => {
					commands.deleteRange(range);
					commands.insertContent(`${match[1]}. `);
				},
			}),
		];
	},
});
