import { type Editor, EditorContent, useEditor } from "@tiptap/react";
import { useCallback, useEffect, useRef } from "react";
import { useEditorConfig, useEditorStyleHandlers } from "./hooks";
import {
	applyVerticalAlignToEditor,
	detectVerticalAlign,
} from "./utils/editorUtils";

interface RichTextEditorProps {
	content: string;
	onChange: (content: string) => void;
	setActiveEditor: (editor: Editor | null) => void;
	verticalAlign?: "top" | "middle" | "bottom";
	setIsTextEditorFocused: (focused: boolean) => void;
	/**
	 * Optional callback that will be invoked when the underlying editor loses focus.
	 * This is useful for higher-level components (e.g. TableEditor) that need to
	 * react to the editor exiting its editing mode.
	 */
	onBlurCallback?: () => void;
	defaultFontSize: string; // Made required to ensure address elements always get 9.5pt
}

export function RichTextEditor({
	content,
	onChange,
	setActiveEditor,
	verticalAlign: initialVerticalAlign,
	setIsTextEditorFocused,
	onBlurCallback,
	defaultFontSize,
}: RichTextEditorProps) {
	const editorRef = useRef<HTMLDivElement>(null);

	// Use extracted configuration and handlers
	const { extensions, parseOptions } = useEditorConfig();
	const { onFocus, onBlur, onCreate, onUpdate, onSelectionUpdate } =
		useEditorStyleHandlers({
			defaultFontSize,
			initialVerticalAlign,
			setIsTextEditorFocused,
			onBlurCallback,
			onChange,
		});

	// Apply vertical alignment to editor element
	const applyVerticalAlign = useCallback((verticalAlign: string | null) => {
		applyVerticalAlignToEditor(editorRef, verticalAlign);
	}, []);

	const editor = useEditor({
		extensions,
		content: content || "<p></p>",
		editable: true,
		parseOptions,
		onFocus,
		onBlur,
		onCreate: ({ editor }) => onCreate({ editor }, content),
		onUpdate: ({ editor }) => {
			onUpdate({ editor });

			// Handle vertical alignment
			const html = editor.getHTML();
			const verticalAlign =
				editor.getAttributes("textStyle")?.verticalAlign ||
				detectVerticalAlign(html);
			applyVerticalAlign(verticalAlign);
		},
		onSelectionUpdate,
	});

	// Set active editor and cleanup
	useEffect(() => {
		if (editor && setActiveEditor) {
			setActiveEditor(editor);
			return () => setActiveEditor(null);
		}
	}, [editor, setActiveEditor]);

	// Apply vertical alignment when it changes
	useEffect(() => {
		if (editor && initialVerticalAlign) {
			editor
				.chain()
				.setMark("textStyle", { verticalAlign: initialVerticalAlign })
				.run();
			applyVerticalAlign(initialVerticalAlign);
		}
	}, [initialVerticalAlign, editor, applyVerticalAlign]);

	return (
		<div
			className="w-full h-full"
			ref={editorRef}
			style={{ padding: 0, margin: 0 }}
		>
			<EditorContent
				editor={editor}
				className="max-w-none w-full h-full focus:outline-none overflow-hidden"
				style={{
					overflowWrap: "break-word",
					wordWrap: "break-word",
					wordBreak: "break-word",
					whiteSpace: "pre-wrap",
					maxWidth: "100%",
					display: "flex",
					flexDirection: "column",
					height: "100%",
					padding: 0,
					margin: 0,
				}}
			/>
		</div>
	);
}

export type { Editor } from "@tiptap/react";
