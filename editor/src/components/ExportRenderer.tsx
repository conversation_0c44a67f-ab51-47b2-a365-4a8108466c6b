import React from "react";
import { CropMarks } from "@/components/CropMarks";
import { EditableElement } from "@/components/elements/EditableElement";
import { FoldMarks } from "@/components/FoldMarks";
import type { Element } from "@/types/element";
import type { PageSettings } from "@/types/page";
import type { PageFormat, TestDataRecord } from "@/utils/apiService";

/**
 * Export Renderer component that uses the same EditableElement component
 * as the main editor but configures it for export rendering
 */

interface ExportElementProps {
	element: Element;
	previewMode?: boolean;
	childElements?: Element[];
	editingBlockId?: string | null;
	globalSelectedElementId?: string | null;
	testData?: TestDataRecord[];
	selectedTestDataIndex?: number;
	highlightVariables?: boolean;
}

/**
 * Custom styles for export preview to override editor styles
 * TODO: Consider moving these to a CSS module or integrating with Tailwind if possible
 */
const exportPreviewStyles = `
/* Page container styles */
.export-document {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
    padding: 2rem;
    background-color: #f5f5f5;
}

.export-page {
    position: relative;
    background: white;
    box-shadow: 0 0 10px rgba(0,0,0,0.2);
    overflow: hidden !important; /* Elements outside paper+trim are hidden */
    transform-origin: top center;
}
`;

/**
 * Wrapper component for EditableElement that disables editing functionality
 * but maintains the same rendering logic
 */
export const ExportElement: React.FC<ExportElementProps> = ({
	element,
	previewMode = true,
	childElements,
	editingBlockId,
	globalSelectedElementId,
	testData = [],
	selectedTestDataIndex = -1,
	highlightVariables = false,
}) => {
	// Create a dummy handler that does nothing
	const noopHandler = () => {};

	return (
		<EditableElement
			element={element}
			onUpdate={noopHandler}
			onDelete={noopHandler}
			onCopy={noopHandler}
			onCut={noopHandler}
			isSelected={false}
			onSelect={noopHandler}
			setActiveEditor={noopHandler}
			onEditingChange={noopHandler}
			highlightVariables={highlightVariables}
			isExportMode={true}
			previewMode={previewMode}
			showElementBorders={false}
			key={element.id}
			childElements={childElements}
			editingBlockId={editingBlockId === undefined ? null : editingBlockId}
			globalSelectedElementId={
				globalSelectedElementId === undefined ? null : globalSelectedElementId
			}
			testData={testData}
			selectedTestDataIndex={selectedTestDataIndex}
		/>
	);
};

interface ExportPageProps {
	page: PageSettings;
	elements: Element[];
	previewMode?: boolean;
	editingBlockId?: string | null;
	globalSelectedElementId?: string | null;
	apiFormats?: PageFormat[];
	testData?: TestDataRecord[];
	selectedTestDataIndex?: number;
	highlightVariables?: boolean;
}

/**
 * Component to render a single page for export preview
 */
export const ExportPage: React.FC<ExportPageProps> = ({
	page,
	elements,
	previewMode = true,
	editingBlockId,
	globalSelectedElementId,
	apiFormats,
	testData = [],
	selectedTestDataIndex = -1,
	highlightVariables = false,
}) => {
	const trimLeftCm = page.trim_left ?? 0;
	const trimRightCm = page.trim_right ?? 0;
	const trimTopCm = page.trim_top ?? 0;
	const trimBottomCm = page.trim_bottom ?? 0;

	const outerWidthCm = page.width + trimLeftCm + trimRightCm;
	const outerHeightCm = page.height + trimTopCm + trimBottomCm;

	// Get only top-level elements for this page (no parentId)
	const topLevelElementsOnPage = elements.filter(
		(element) => element.currentPageId === page.id && !element.parentId,
	);

	// Filter out elements that are positioned beyond the page bounds (including trim)
	const filteredElements = topLevelElementsOnPage.filter(
		(element) =>
			!(element.x > outerWidthCm * 10) && !(element.y > outerHeightCm * 10),
	);
	return (
		<div // Outer container: Paper + Trim. This IS the "page" for export.
			className={`export-page ${page.format.toLowerCase()} ${page.orientation || "portrait"}`}
			style={{
				width: `${outerWidthCm}cm`,
				height: `${outerHeightCm}cm`,
				backgroundColor: "white",
				margin: "0 auto",
			}}
			data-page-id={page.id}
		>
			{/* Crop Marks */}
			<CropMarks
				pageWidth={page.width}
				pageHeight={page.height}
				trimLeft={trimLeftCm}
				trimRight={trimRightCm}
				trimTop={trimTopCm}
				trimBottom={trimBottomCm}
			/>

			{/* Fold Marks - Always show hints in export */}
			<FoldMarks
				pageWidth={page.width}
				pageHeight={page.height}
				trimLeft={trimLeftCm}
				trimTop={trimTopCm}
				outerWidth={outerWidthCm}
				outerHeight={outerHeightCm}
				pageFormat={apiFormats?.find((f) => f.name === page.format)}
				showFullLines={false}
			/>

			<div // Inner container: Content Area (where elements are positioned relative to)
				style={{
					position: "absolute",
					top: `${trimTopCm}cm`,
					left: `${trimLeftCm}cm`,
					width: `${page.width}cm`,
					height: `${page.height}cm`,
					// Elements are children here. Their (x,y) are relative to this div.
					// Elements extending from content into trim will be visible.
					// Elements extending beyond trim will be clipped by the outer div.
				}}
			>
				{filteredElements.map((element) => {
					// For each top-level element, find its children if it's a block
					const children =
						element.type === "block" && element.childElementIds
							? elements.filter((el) =>
									element.childElementIds?.includes(el.id),
								)
							: undefined;

					return (
						<ExportElement
							key={element.id}
							element={element}
							previewMode={previewMode}
							childElements={children}
							editingBlockId={editingBlockId}
							globalSelectedElementId={globalSelectedElementId}
							testData={testData}
							selectedTestDataIndex={selectedTestDataIndex}
							highlightVariables={highlightVariables}
						/>
					);
				})}
			</div>
		</div>
	);
};

interface ExportDocumentProps {
	pages: PageSettings[];
	elements: Element[];
	editingBlockId?: string | null;
	globalSelectedElementId?: string | null;
	apiFormats?: PageFormat[];
	testData?: TestDataRecord[];
	selectedTestDataIndex?: number;
	highlightVariables?: boolean;
}

/**
 * Component to render the entire document for export preview
 */
export const ExportDocument: React.FC<ExportDocumentProps> = ({
	pages,
	elements,
	editingBlockId,
	globalSelectedElementId,
	apiFormats,
	testData = [],
	selectedTestDataIndex = -1,
	highlightVariables = false,
}) => {
	// Inject export-specific styles when component mounts
	// Consider adding styles via CSS import or styled components instead
	React.useEffect(() => {
		// Check if the styles are already injected
		let styleElement = document.getElementById("export-preview-styles");
		if (!styleElement) {
			styleElement = document.createElement("style");
			styleElement.id = "export-preview-styles";
			document.head.appendChild(styleElement);
		}
		// Ensure the innerHTML is updated even if the element exists
		styleElement.innerHTML = exportPreviewStyles;

		return () => {
			// Clean up styles when component unmounts
			const styleElementToRemove = document.getElementById(
				"export-preview-styles",
			);
			if (styleElementToRemove) {
				styleElementToRemove.remove();
			}
		};
	}, []); // Run only once on mount

	// Group elements by page
	const elementsByPage = pages.map(
		(page) => elements.filter((element) => element.currentPageId === page.id), // Only top-level elements for a page
	);

	return (
		<div className="export-document">
			{pages.map((page, index) => (
				<React.Fragment key={page.id}>
					<h3 style={{ textAlign: "center", margin: "0 0 1rem 0" }}>
						Page {index + 1} ({page.format})
					</h3>
					<ExportPage
						page={page}
						elements={elementsByPage[index]}
						editingBlockId={editingBlockId}
						globalSelectedElementId={globalSelectedElementId}
						apiFormats={apiFormats}
						testData={testData}
						selectedTestDataIndex={selectedTestDataIndex}
						highlightVariables={highlightVariables}
					/>
				</React.Fragment>
			))}
		</div>
	);
};
