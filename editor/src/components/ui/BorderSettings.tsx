import { useEffect, useState } from "react";
import type { Element } from "@/types/element";
import type { ColorInfo } from "@/utils/apiService";
import { ColorPicker } from "./ColorPicker";
import { Input } from "./input";
import { Label } from "./label";

interface BorderSettingsProps {
	element: Element | null;
	onUpdate: (updatedElement: Element) => void;
	apiColors: ColorInfo[];
	isLoadingColors: boolean;
	colorError: string | null;
}

export function BorderSettings({
	element,
	onUpdate,
	apiColors,
	isLoadingColors,
	colorError,
}: BorderSettingsProps) {
	const [borderWidth, setBorderWidth] = useState<number>(0);
	const [borderColor, setBorderColor] = useState<string>("#000000");

	// Initialize border values from element
	useEffect(() => {
		if (element) {
			setBorderWidth(element.borderWidth || 0);
			setBorderColor(element.borderColor || "#000000");
		}
	}, [element]);

	const handleBorderWidthChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const value = parseFloat(e.target.value) || 0;
		setBorderWidth(value);

		if (element) {
			onUpdate({
				...element,
				borderWidth: value,
			});
		}
	};

	const handleBorderColorChange = (newColor: string) => {
		setBorderColor(newColor);

		if (element) {
			onUpdate({
				...element,
				borderColor: newColor,
			});
		}
	};

	// Determine if the component should be disabled
	const isDisabled = !element || element.type !== "shape";

	return (
		<div className="space-y-4">
			<div>
				<Label className="text-sm font-medium">Rahmen</Label>
			</div>

			<div className="space-y-3">
				<div className="space-y-1">
					<Label
						htmlFor="border-width"
						className={`text-xs ${isDisabled ? "opacity-50" : ""}`}
					>
						Stärke (pt)
					</Label>
					<Input
						id="border-width"
						type="number"
						min="0"
						step="0.1"
						value={borderWidth}
						onChange={handleBorderWidthChange}
						className="h-8"
						disabled={isDisabled}
					/>
				</div>

				<div className="space-y-1">
					<Label
						htmlFor="border-color"
						className={`text-xs ${isDisabled ? "opacity-50" : ""}`}
					>
						Farbe
					</Label>
					<ColorPicker
						label=""
						currentColor={borderColor}
						onColorChange={handleBorderColorChange}
						apiColors={apiColors}
						isLoadingColors={isLoadingColors}
						colorError={colorError}
					/>
				</div>
			</div>
		</div>
	);
}
