import { LayoutTemplate } from "lucide-react";
import type { Element } from "@/types/element";
import type { TemplateInfo } from "@/utils/apiService";
import {
	ToolbarEmpty,
	ToolbarError,
	ToolbarHeader,
	ToolbarLoading,
} from "./ToolbarBase";

interface TemplateToolbarProps {
	templates: TemplateInfo[];
	onInsertTemplate: (
		templateData: { blockElement: Element; childElements: Element[] },
		insertX?: number,
		insertY?: number,
	) => void;
	isLoading: boolean;
	error: string | null;
}

export function TemplateToolbar({
	templates,
	onInsertTemplate,
	isLoading,
	error,
}: TemplateToolbarProps) {
	const handleTemplateClick = async (template: TemplateInfo) => {
		try {
			// Parse the template JSON data
			const templateData = JSON.parse(template.json) as {
				blockElement: Element;
				childElements: Element[];
			};

			onInsertTemplate(templateData);
		} catch (error) {
			console.error("Failed to load template:", error);
		}
	};

	return (
		<div className="flex flex-col h-full">
			<ToolbarHeader title="Vorlagen" />

			{error && <ToolbarError message={error} className="mb-3" />}

			{isLoading ? (
				<ToolbarLoading />
			) : templates.length > 0 ? (
				<div className="flex-1 overflow-y-auto space-y-3 pr-2">
					{templates.map((template) => (
						<button
							type="button"
							key={template.id}
							onClick={() => handleTemplateClick(template)}
							className="block w-full text-left p-2 rounded-md transition-colors hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
						>
							<div className="flex items-start gap-2 w-full">
								<LayoutTemplate className="w-4 h-4 flex-shrink-0 mt-0.5 text-blue-600" />
								<div className="flex-1 min-w-0">
									<span className="font-medium text-blue-600 hover:text-blue-800 break-words leading-tight">
										{template.name}
									</span>
									<p className="text-xs text-gray-500 mt-0.5">
										von {template.user}
									</p>
									<p className="text-xs text-gray-500 mt-0.5">
										{new Date(template.createdAt).toLocaleString("de-DE")}
									</p>
								</div>
							</div>
						</button>
					))}
				</div>
			) : (
				<ToolbarEmpty message="Keine Vorlagen verfügbar." />
			)}
		</div>
	);
}
