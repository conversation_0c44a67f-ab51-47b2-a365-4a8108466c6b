import { Table } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { PositionSizeSettings } from "@/components/sidebar/PositionSizeSettings";
import { TableCreateDialog } from "@/components/table/TableCreateDialog";
import type { Element } from "@/types/element";
import type { TableCell, TableProperties } from "@/types/table";
import type { ColorInfo } from "@/utils/apiService";
import { getScrollAwarePosition } from "@/utils/elementHelpers";
import {
	applyBordersToLogicalState,
	createInitialBorderState,
	isCellSelectedInLogicalGrid,
	isSelectionSinglePhysicalCell,
	logicalPositionToCell,
} from "@/utils/tableUtils";
import { ptToPx } from "@/utils/unitConversion";
import { BorderSelectionControl } from "../ui/BorderSelectionControl";
import { Button } from "../ui/button";
import { ColorPicker } from "../ui/ColorPicker";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";

interface TableFormatToolbarProps {
	onAddElement?: (element: Element) => void;
	selectedElement?: Element | null;
	onUpdateElement?: (element: Element) => void;
	// Add any other props needed for table formatting, e.g., editor-like state if cells are edited directly in sidebar
	apiColors: ColorInfo[];
	isLoadingColors: boolean;
	colorError: string | null;
}

export function TableFormatToolbar({
	onAddElement,
	selectedElement,
	onUpdateElement,
	apiColors,
	isLoadingColors,
	colorError,
}: TableFormatToolbarProps) {
	const [activeTab, setActiveTab] = useState<string>("layout");
	const [isTablePopoverOpen, setIsTablePopoverOpen] = useState(false);
	const prevSelectedTableIdRef = useRef<string | undefined | null>(null); // Keep track of the previously selected table ID
	const prevSelectionRef = useRef<
		| { start: { row: number; col: number }; end: { row: number; col: number } }
		| null
		| undefined
	>(null); // Keep track of the previous selection

	// Ensure selectedElement is a table
	const currentTableElement =
		selectedElement?.type === "table" ? selectedElement : null;
	const tableProperties = currentTableElement?.tableProperties;

	// Ref to track the most recent table cells for border operations
	// This gets updated immediately when borders are applied to ensure subsequent operations use the latest state
	const latestCellsRef = useRef<TableCell[][] | null>(null);

	// Update ref when selectedElement changes
	useEffect(() => {
		if (currentTableElement?.tableProperties?.cells) {
			latestCellsRef.current = currentTableElement.tableProperties.cells;
		} else {
			latestCellsRef.current = null;
		}
	}, [currentTableElement]);

	const handleCreateTable = (properties: TableProperties) => {
		if (onAddElement) {
			const { x, y } = getScrollAwarePosition();

			const newElement: Element = {
				id: uuidv4(),
				type: "table",
				x,
				y,
				width: properties.columnWidths?.reduce((a, b) => a + b, 0) || 0, // Sum of column widths
				height: properties.rowHeights?.reduce((a, b) => a + b, 0) || 0, // Sum of row heights
				rotation: 0,
				currentPageId: "", // This should be set based on the current page context
				tableProperties: properties,
			};
			onAddElement(newElement);
			setIsTablePopoverOpen(false);
		}
	};

	const handleUpdateTableProperties = (
		updatedProps: Partial<TableProperties>,
	) => {
		if (
			currentTableElement &&
			onUpdateElement &&
			currentTableElement.tableProperties
		) {
			const newTableProperties = {
				...currentTableElement.tableProperties,
				...updatedProps,
			};
			const updatedElement = {
				...currentTableElement,
				tableProperties: newTableProperties,
			};

			// Only update ref for border-related changes to avoid interfering with border analysis
			// This ensures we only track border changes, not other table property changes
			if (updatedProps.cells) {
				latestCellsRef.current = updatedProps.cells;
			}

			// Also update the parent component
			onUpdateElement(updatedElement);
		}
	};

	const handleUpdateElementLayout = (updatedElement: Element) => {
		if (onUpdateElement && currentTableElement) {
			onUpdateElement({
				...currentTableElement,
				...updatedElement, // Apply layout changes (x, y, width, height, rotation)
			});
		}
	};

	useEffect(() => {
		const currentTableId = currentTableElement?.id;
		const currentSelection = currentTableElement?.tableProperties?.selection;
		const hasAnySelection =
			currentSelection !== null && currentSelection !== undefined;

		// Check if selection has actually changed
		const selectionChanged =
			(prevSelectionRef.current === null && currentSelection !== null) ||
			(prevSelectionRef.current !== null && currentSelection === null) ||
			(prevSelectionRef.current &&
				currentSelection &&
				(prevSelectionRef.current.start.row !== currentSelection.start.row ||
					prevSelectionRef.current.start.col !== currentSelection.start.col ||
					prevSelectionRef.current.end.row !== currentSelection.end.row ||
					prevSelectionRef.current.end.col !== currentSelection.end.col));

		if (currentTableId) {
			if (hasAnySelection && selectionChanged) {
				// Switch to properties tab only when cell selection changes
				setActiveTab("properties");
			} else if (prevSelectedTableIdRef.current !== currentTableId) {
				// Only switch to layout tab if it's a new table with no cell selection
				setActiveTab("layout");
			}
			prevSelectedTableIdRef.current = currentTableId;
			prevSelectionRef.current = currentSelection;
		} else {
			// If no table is selected, and a table was previously selected, clear the ref.
			// This ensures that if a table is deselected, and then a *new* table is selected,
			// it will correctly default to the layout tab.
			if (prevSelectedTableIdRef.current) {
				prevSelectedTableIdRef.current = null;
			}
			prevSelectionRef.current = null;
			// Optionally, switch to a default tab or disable tabs if no element is selected.
			// For now, activeTab remains, which is fine if tabs are disabled.
		}
	}, [currentTableElement]);

	const renderSpanControls = () => {
		// Determine if exactly one cell is selected in the current table
		// For merged cells, we need to check if the selection represents a single actual cell
		let singleCellSelected = false;

		if (currentTableElement && tableProperties?.selection) {
			const { start, end } = tableProperties.selection;

			// Check if this is a single logical position (no expansion)
			const isSimpleSingleCell = start.row === end.row && start.col === end.col;

			if (isSimpleSingleCell) {
				singleCellSelected = true;
			} else {
				// For expanded selections, check if they represent a single merged cell
				// Get the cell at the start position
				const cellPosition = logicalPositionToCell(
					tableProperties.cells,
					start.row,
					start.col,
					tableProperties.rows,
					tableProperties.columns,
				);

				if (cellPosition) {
					const cell =
						tableProperties.cells[cellPosition.rowIndex][cellPosition.colIndex];
					if (cell) {
						// Calculate the expected end position for this cell's span
						const expectedEndRow = start.row + cell.rowspan - 1;
						const expectedEndCol = start.col + cell.colspan - 1;

						// Check if the selection exactly matches this cell's span
						if (end.row === expectedEndRow && end.col === expectedEndCol) {
							singleCellSelected = true;
						}
					}
				}
			}
		}

		// Default placeholder values when no table or no selection
		let currentColspan = 1;
		let currentRowspan = 1;
		let maxColspan = 1;
		let maxRowspan = 1;

		if (singleCellSelected && tableProperties && tableProperties.selection) {
			const { row, col } = tableProperties.selection.start;
			// Convert logical position to actual cell array indices
			const cellPosition = logicalPositionToCell(
				tableProperties.cells,
				row,
				col,
				tableProperties.rows,
				tableProperties.columns,
			);

			if (cellPosition) {
				const cell =
					tableProperties.cells[cellPosition.rowIndex][cellPosition.colIndex];
				if (cell) {
					currentColspan = cell.colspan || 1;
					currentRowspan = cell.rowspan || 1;
				}
			}
			maxColspan = tableProperties.columns - col;
			maxRowspan = tableProperties.rows - row;
		} else if (tableProperties) {
			// If table exists but no single selection, limit max to full table dimensions
			maxColspan = tableProperties.columns;
			maxRowspan = tableProperties.rows;
		}

		const onSpanChange = (
			newColspan: number | null,
			newRowspan: number | null,
		) => {
			if (!singleCellSelected || !tableProperties || !tableProperties.selection)
				return;

			const { row, col } = tableProperties.selection.start;

			// Convert logical position to actual cell array indices
			const cellPosition = logicalPositionToCell(
				tableProperties.cells,
				row,
				col,
				tableProperties.rows,
				tableProperties.columns,
			);

			if (!cellPosition) {
				console.warn(
					`Could not find cell at logical position [${row}][${col}]`,
				);
				return;
			}

			// Deep clone cells to avoid state mutation
			const newCells = JSON.parse(
				JSON.stringify(tableProperties.cells),
			) as TableCell[][];

			const targetCell = newCells[cellPosition.rowIndex][cellPosition.colIndex];
			if (!targetCell) {
				console.warn(
					`Target cell not found at [${cellPosition.rowIndex}][${cellPosition.colIndex}]`,
				);
				return;
			}

			/* ------------------------- COLSPAN HANDLING ------------------------- */
			if (newColspan !== null && newColspan !== targetCell.colspan) {
				const delta = newColspan - targetCell.colspan; // positive = increase, negative = decrease

				if (delta > 0) {
					// Increasing colspan: we need to hide columns to the right
					// Store the cells that will be hidden instead of deleting them
					let remaining = delta;
					const cursor = cellPosition.colIndex + 1;
					const hiddenCells: TableCell[] = targetCell.hiddenCells || [];

					while (
						remaining > 0 &&
						cursor < newCells[cellPosition.rowIndex].length
					) {
						const nextCell = newCells[cellPosition.rowIndex][cursor];
						if (nextCell.colspan <= remaining) {
							// Store the whole cell and remove it from the array
							hiddenCells.push({ ...nextCell });
							remaining -= nextCell.colspan;
							newCells[cellPosition.rowIndex].splice(cursor, 1);
							// Do NOT advance cursor – items shift left
						} else {
							// Create a new cell for the portion that will be hidden
							const hiddenPortion: TableCell = {
								...nextCell,
								colspan: remaining,
							};
							hiddenCells.push(hiddenPortion);
							// Shrink the next cell's colspan
							nextCell.colspan -= remaining;
							remaining = 0;
						}
					}

					// Store the hidden cells in the target cell
					targetCell.hiddenCells =
						hiddenCells.length > 0 ? hiddenCells : undefined;
				} else if (delta < 0) {
					// Decreasing colspan: restore hidden cells if they exist
					const cellsToRestore = -delta;
					const hiddenCells = targetCell.hiddenCells || [];
					let restoredColspan = 0;

					// Restore cells from hiddenCells first
					for (
						let i = 0;
						i < hiddenCells.length && restoredColspan < cellsToRestore;
						i++
					) {
						const hiddenCell = hiddenCells[i];
						const colspanToUse = Math.min(
							hiddenCell.colspan,
							cellsToRestore - restoredColspan,
						);

						if (colspanToUse === hiddenCell.colspan) {
							// Use the entire hidden cell
							newCells[cellPosition.rowIndex].splice(
								cellPosition.colIndex + 1,
								0,
								{ ...hiddenCell },
							);
						} else {
							// Use only part of the hidden cell
							newCells[cellPosition.rowIndex].splice(
								cellPosition.colIndex + 1,
								0,
								{ ...hiddenCell, colspan: colspanToUse },
							);
							// Keep the remaining portion in hiddenCells
							hiddenCell.colspan -= colspanToUse;
							hiddenCells[i] = hiddenCell;
						}

						restoredColspan += colspanToUse;
					}

					// Remove the used hidden cells
					const remainingHiddenCells = hiddenCells.filter(
						(cell) => cell.colspan > 0,
					);
					targetCell.hiddenCells =
						remainingHiddenCells.length > 0 ? remainingHiddenCells : undefined;

					// If we still need more cells, create new default ones
					while (restoredColspan < cellsToRestore) {
						const defaultCell: TableCell = {
							content: "",
							colspan: 1,
							rowspan: 1,
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
							backgroundColor: "transparent",
							verticalAlign: "top",
						};

						newCells[cellPosition.rowIndex].splice(
							cellPosition.colIndex + 1,
							0,
							{ ...defaultCell },
						);
						restoredColspan++;
					}
				}

				// Finally, update the target cell's colspan to the desired value
				targetCell.colspan = newColspan;
			}

			/* ------------------------- ROWSPAN HANDLING ------------------------- */
			if (newRowspan !== null && newRowspan !== targetCell.rowspan) {
				// Basic rowspan update – extending/shrinking vertically can get complex.
				// For now, simply update the rowspan; additional logic can be added later
				targetCell.rowspan = newRowspan;
			}

			handleUpdateTableProperties({ cells: newCells });
		};

		return (
			<div className="space-y-2" key="span-controls">
				<div className="text-sm font-medium">Zellenspanne</div>
				<div className="grid grid-cols-2 gap-2 items-center">
					<div>
						<Label htmlFor="colspan-input" className="text-xs">
							Colspan
						</Label>
						<Input
							id="colspan-input"
							type="number"
							min={1}
							max={maxColspan}
							value={currentColspan}
							disabled={!singleCellSelected}
							onChange={(e) => {
								let value = parseInt(e.target.value, 10);
								if (Number.isNaN(value) || value < 1) value = 1;
								if (value > maxColspan) value = maxColspan;
								onSpanChange(value, null);
							}}
						/>
					</div>
					<div>
						<Label htmlFor="rowspan-input" className="text-xs">
							Rowspan
						</Label>
						<Input
							id="rowspan-input"
							type="number"
							min={1}
							max={maxRowspan}
							value={currentRowspan}
							disabled={!singleCellSelected}
							onChange={(e) => {
								let value = parseInt(e.target.value, 10);
								if (Number.isNaN(value) || value < 1) value = 1;
								if (value > maxRowspan) value = maxRowspan;
								onSpanChange(null, value);
							}}
						/>
					</div>
				</div>
			</div>
		);
	};

	return (
		<div className="space-y-4 format-toolbar">
			{onAddElement && (
				<div className="space-y-2">
					<Popover
						open={isTablePopoverOpen}
						onOpenChange={setIsTablePopoverOpen}
					>
						<PopoverTrigger asChild>
							<Button
								variant="outline"
								className="w-full flex items-center justify-center gap-2"
							>
								<Table className="h-4 w-4" />
								Tabelle hinzufügen
							</Button>
						</PopoverTrigger>
						<PopoverContent className="w-auto p-0" align="start">
							<TableCreateDialog
								onCreateTable={handleCreateTable}
								onClose={() => setIsTablePopoverOpen(false)}
							/>
						</PopoverContent>
					</Popover>
				</div>
			)}

			<Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
				<TabsList className="grid w-full grid-cols-2">
					<TabsTrigger value="properties">Eigenschaften</TabsTrigger>
					<TabsTrigger value="layout">Position</TabsTrigger>
				</TabsList>

				<TabsContent value="properties" className="space-y-4 mt-2">
					{/* Always show span controls at the top */}
					{renderSpanControls()}

					{currentTableElement && tableProperties ? (
						<>
							<div className="space-y-2">
								<div className="text-sm font-medium">Rahmen</div>
								{/* Placeholder for border controls */}
								<p className="text-xs text-gray-500">
									Rahmenstil, -farbe, -stärke hier einfügen.
								</p>
								<div className="space-y-1"></div>
							</div>
							<div className="space-y-2">
								<div className="text-sm font-medium">
									Hintergrund (Ausgewählte Zellen)
								</div>
								{(() => {
									// Get current background colors of selected cells
									const selection = tableProperties.selection;
									let currentCellColor: string | undefined;

									if (selection) {
										const selectedCellColors = new Set<string>();

										// Collect all background colors from selected cells
										for (
											let row = selection.start.row;
											row <= selection.end.row;
											row++
										) {
											for (
												let col = selection.start.col;
												col <= selection.end.col;
												col++
											) {
												const cellPosition = logicalPositionToCell(
													tableProperties.cells,
													row,
													col,
													tableProperties.rows,
													tableProperties.columns,
												);
												if (cellPosition) {
													const cell =
														tableProperties.cells[cellPosition.rowIndex][
															cellPosition.colIndex
														];
													const bgColor = cell.backgroundColor || null;
													if (bgColor) {
														selectedCellColors.add(bgColor);
													}
												}
											}
										}

										// If all selected cells have the same color, use that as current color
										if (selectedCellColors.size === 1) {
											currentCellColor = Array.from(selectedCellColors)[0];
										}
										// If no cells have colors or mixed colors, currentCellColor stays undefined
									}

									return (
										<ColorPicker
											label=""
											currentColor={currentCellColor}
											onColorChange={(newColor) => {
												handleUpdateTableProperties({
													selectedCellsBackgroundColor: newColor,
												});
											}}
											apiColors={apiColors}
											isLoadingColors={isLoadingColors}
											colorError={colorError}
										/>
									);
								})()}
							</div>
							{/* Border Selection UI for multi-selected cells */}
							{(() => {
								const selection = tableProperties.selection;

								if (selection) {
									// Use the most recent cells for the BorderSelectionControl
									// This ensures it analyzes the latest border state, not stale props
									const currentCells =
										latestCellsRef.current || tableProperties.cells;
									const currentTableProperties = {
										...tableProperties,
										cells: currentCells,
									};

									// Create a key that changes when selection type changes to force reset
									const isMultiCell = !isSelectionSinglePhysicalCell(
										currentCells,
										selection,
									);
									const selectionKey = `${currentTableElement?.id}-${isMultiCell ? "multi" : "single"}`;

									// Render if ANY cell(s) are selected
									return (
										<BorderSelectionControl
											key={selectionKey}
											tableProperties={currentTableProperties}
											onUpdateCellBorders={(
												currentSelection,
												activeLines,
												borderWidthPt,
												borderColorHex,
											) => {
												// 1) Convert pt input to px & normalize color
												const newWidthPx =
													borderWidthPt !== null ? ptToPx(borderWidthPt) : null;
												const newColorHex = borderColorHex; // May be null

												// 2) Ensure a borderState exists
												let borderState = tableProperties.borderState;
												if (!borderState) {
													borderState =
														createInitialBorderState(tableProperties);
												}

												// 3) Apply the border modifications. NOTE: this mutates tableProperties.cells
												const updatedBorderState = applyBordersToLogicalState(
													borderState,
													currentSelection,
													activeLines,
													newWidthPx,
													newColorHex,
													tableProperties,
												);

												// 4) Deep-clone the now-mutated cells so we can persist them immutably
												const clonedCellsWithBorders: TableCell[][] =
													JSON.parse(JSON.stringify(tableProperties.cells));

												// 5) Overlay selected-cells background color onto the clone (after borders are in place)
												const finalCells = clonedCellsWithBorders.map(
													(row: TableCell[], rIdx: number) =>
														row.map((cell: TableCell, cIdx: number) => {
															// Apply global selection background if configured and this cell is part of selection
															if (
																tableProperties.selectedCellsBackgroundColor &&
																currentSelection &&
																isCellSelectedInLogicalGrid(
																	clonedCellsWithBorders,
																	rIdx,
																	cIdx,
																	currentSelection,
																)
															) {
																return {
																	...cell,
																	backgroundColor:
																		tableProperties.selectedCellsBackgroundColor,
																};
															}
															return cell;
														}),
												);

												// 6) Persist both cells & borderState back to element
												handleUpdateTableProperties({
													cells: finalCells,
													borderState: updatedBorderState,
												});
											}}
											selectedElementId={currentTableElement?.id}
											apiColors={apiColors}
											isLoadingColors={isLoadingColors}
											colorError={colorError}
										/>
									);
								}
								return null;
							})()}
						</>
					) : (
						<p className="text-sm text-gray-500">
							Wählen Sie eine Tabelle aus, um deren Eigenschaften zu bearbeiten.
						</p>
					)}
				</TabsContent>

				<TabsContent value="layout" className="space-y-4 mt-2">
					{currentTableElement ? (
						<PositionSizeSettings
							element={currentTableElement}
							onUpdate={handleUpdateElementLayout}
						/>
					) : (
						<p className="text-sm text-gray-500">
							Wählen Sie eine Tabelle aus, um deren Position und Größe zu
							bearbeiten.
						</p>
					)}
				</TabsContent>
			</Tabs>
		</div>
	);
}
