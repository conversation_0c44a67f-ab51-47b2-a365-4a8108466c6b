import type { Editor } from "@tiptap/react";
import {
	AlignCenter,
	AlignJustify,
	AlignLeft,
	AlignRight,
	AlignVerticalJustifyCenter,
	AlignVerticalJustifyEnd,
	AlignVerticalJustifyStart,
	Bold,
	FileText,
	Italic,
	List,
	ListOrdered,
	Loader2,
	Minus,
	Plus,
	Search,
	Strikethrough,
	Subscript as SubscriptIcon,
	Superscript as SuperscriptIcon,
	Underline,
} from "lucide-react";
import {
	type ChangeEvent,
	type FocusEvent,
	type KeyboardEvent,
	useEffect,
	useMemo,
	useRef,
	useState,
} from "react";
import { v4 as uuidv4 } from "uuid";
import type { Element } from "@/types/element";
import type { ColorInfo, FontInfo } from "@/utils/apiService";
import { getScrollAwarePosition } from "@/utils/elementHelpers";
import { PositionSizeSettings } from "../sidebar/PositionSizeSettings";
import { Button } from "../ui/button";
import { ColorPicker } from "../ui/ColorPicker";
import { Input } from "../ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../ui/tabs";
import { Toggle } from "../ui/toggle";

interface TextFormatToolbarProps {
	editor: Editor | null;
	onAddElement?: (element: Element) => void;
	selectedElement?: Element | null;
	onUpdateElement?: (element: Element) => void;
	isEditing?: boolean;
	elements?: Element[];
	documentVariables?: string[];
	apiFonts: FontInfo[];
	isLoadingFonts: boolean;
	loadedFonts: Set<string>;
	determineWeight: (style: string | undefined) => string;
	apiColors: ColorInfo[];
	isLoadingColors: boolean;
	colorError: string | null;
}

interface FontPickerProps {
	editor: Editor | null;
	apiFonts: FontInfo[];
	isLoading: boolean;
	loadedFonts: Set<string>;
	determineWeight: (style: string | undefined) => string;
}

interface FontSizePickerProps {
	editor: Editor | null;
	selectedElement?: Element | null;
}

interface LineHeightPickerProps {
	editor: Editor | null;
}

interface LetterSpacingPickerProps {
	editor: Editor | null;
}

export function TextFormatToolbar({
	editor,
	onAddElement,
	selectedElement,
	onUpdateElement,
	isEditing = false,
	documentVariables = [],
	apiFonts,
	isLoadingFonts,
	loadedFonts,
	determineWeight,
	apiColors = [],
	isLoadingColors = false,
	colorError = null,
}: TextFormatToolbarProps) {
	const [activeTab, setActiveTab] = useState<string>("position");
	const [variableSearch, setVariableSearch] = useState<string>("");
	const prevIsEditing = useRef<boolean>(isEditing);
	const [isBulletActive, setIsBulletActive] = useState(false);
	const [isNumberActive, setIsNumberActive] = useState(false);

	// Filter variables based on search term
	const filteredVariables = useMemo(() => {
		if (!variableSearch.trim()) {
			return documentVariables;
		}
		const searchLower = variableSearch.toLowerCase();
		return documentVariables.filter((variable) =>
			variable.toLowerCase().includes(searchLower),
		);
	}, [documentVariables, variableSearch]);

	// Update active list state based on current selection content
	useEffect(() => {
		if (!editor) {
			setIsBulletActive(false);
			setIsNumberActive(false);
			return;
		}

		const checkActiveState = () => {
			const { state } = editor;
			const { $from } = state.selection;
			const node = $from.node($from.depth);

			if (node && node.type.name === "paragraph") {
				const text = node.textContent;
				const bulletPrefix = "• ";
				const numberRegex = /^\d+\.\s/;
				setIsBulletActive(text.startsWith(bulletPrefix));
				setIsNumberActive(numberRegex.test(text));
			} else {
				setIsBulletActive(false);
				setIsNumberActive(false);
			}
		};

		// Check immediately and subscribe to updates
		checkActiveState();
		editor.on("selectionUpdate", checkActiveState);
		editor.on("transaction", checkActiveState); // Also check on transaction for content changes

		return () => {
			editor.off("selectionUpdate", checkActiveState);
			editor.off("transaction", checkActiveState);
		};
	}, [editor]); // Rerun when editor instance changes

	// Switch to the appropriate tab based on editing state transitions
	useEffect(() => {
		if (selectedElement) {
			// Only switch tabs when transitioning between edit states, not during formatting changes
			if (isEditing && !prevIsEditing.current) {
				// Entered edit mode (transitioned from false to true)
				setActiveTab("formatting");
			} else if (!isEditing && prevIsEditing.current) {
				// Only switch to position when explicitly exiting edit mode
				setActiveTab("position");
			}
			// Otherwise, preserve the current tab selection
		} else {
			// No element selected, default to formatting
			setActiveTab("formatting");
		}

		// Update the ref *after* the check for the next render
		prevIsEditing.current = isEditing;
	}, [selectedElement, isEditing]);

	const handleAddTextElement = () => {
		if (onAddElement) {
			// Get position based on scroll-aware logic
			const { x, y } = getScrollAwarePosition();

			// Create text element with proper dimensions
			const newElement: Element = {
				id: uuidv4(),
				type: "text",
				content: "",
				x,
				y,
				width: 80, // Smaller width (mm)
				height: 40, // Smaller height (mm)
				rotation: 0,
				currentPageId: "",
			};
			onAddElement(newElement);
		}
	};

	const handleUpdateElement = (updatedElement: Element) => {
		if (onUpdateElement) {
			onUpdateElement(updatedElement);
		}
	};

	// Function to insert a variable into the editor
	const insertVariable = (variable: string) => {
		if (editor) {
			// Insert the variable wrapped in double curly braces
			editor.chain().focus().insertContent(`{{${variable}}}`).run();

			// Ensure the content stays within the element boundaries
			if (selectedElement && onUpdateElement) {
				// Get the updated content from the editor
				const updatedContent = editor.getHTML();

				// Update the element with both the dimensions and the new content
				onUpdateElement({
					...selectedElement,
					content: updatedContent,
					width: selectedElement.width,
					height: selectedElement.height,
				});
			}
		}
	};

	return (
		<div className="space-y-4 format-toolbar">
			{onAddElement && (
				<div className="space-y-2">
					<Button
						variant="outline"
						className="w-full flex items-center justify-center gap-2"
						onClick={handleAddTextElement}
					>
						<FileText className="h-4 w-4" />
						Text hinzufügen
					</Button>
				</div>
			)}

			<Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
				<TabsList className="grid w-full grid-cols-2">
					<TabsTrigger value="formatting" disabled={!editor}>
						Textformatierung
					</TabsTrigger>
					<TabsTrigger value="position">Position</TabsTrigger>
				</TabsList>

				<TabsContent value="formatting" className="space-y-4 mt-2">
					<div className="space-y-2">
						<div className="text-sm font-medium">Schrift</div>
						<div className="grid grid-cols-6 gap-1">
							<Toggle
								variant="outline"
								size="sm"
								pressed={editor?.isActive("bold") ?? false}
								onPressedChange={() =>
									editor?.chain().focus().toggleBold().run()
								}
								disabled={!editor}
							>
								<Bold className="h-4 w-4" />
							</Toggle>
							<Toggle
								variant="outline"
								size="sm"
								pressed={editor?.isActive("italic") ?? false}
								onPressedChange={() =>
									editor?.chain().focus().toggleItalic().run()
								}
								disabled={!editor}
							>
								<Italic className="h-4 w-4" />
							</Toggle>
							<Toggle
								variant="outline"
								size="sm"
								pressed={editor?.isActive("underline") ?? false}
								onPressedChange={() =>
									editor?.chain().focus().toggleUnderline().run()
								}
								disabled={!editor}
							>
								<Underline className="h-4 w-4" />
							</Toggle>
							<Toggle
								variant="outline"
								size="sm"
								pressed={editor?.isActive("strike") ?? false}
								onPressedChange={() =>
									editor?.chain().focus().toggleStrike().run()
								}
								disabled={!editor}
							>
								<Strikethrough className="h-4 w-4" />
							</Toggle>
							<Toggle
								variant="outline"
								size="sm"
								pressed={editor?.isActive("superscript") ?? false}
								onPressedChange={() => {
									if (editor?.isActive("subscript")) {
										// If subscript is active, turn it off first
										editor?.chain().focus().toggleSubscript().run();
									}
									editor?.chain().focus().toggleSuperscript().run();
								}}
								disabled={!editor}
							>
								<SuperscriptIcon className="h-4 w-4" />
							</Toggle>
							<Toggle
								variant="outline"
								size="sm"
								pressed={editor?.isActive("subscript") ?? false}
								onPressedChange={() => {
									if (editor?.isActive("superscript")) {
										// If superscript is active, turn it off first
										editor?.chain().focus().toggleSuperscript().run();
									}
									editor?.chain().focus().toggleSubscript().run();
								}}
								disabled={!editor}
							>
								<SubscriptIcon className="h-4 w-4" />
							</Toggle>
						</div>
					</div>

					<div className="space-y-2">
						<div className="text-sm font-medium">Ausrichtung</div>
						<div className="grid grid-cols-4 gap-1">
							<Toggle
								variant="outline"
								size="sm"
								pressed={editor?.isActive({ textAlign: "left" }) ?? false}
								onPressedChange={() =>
									editor?.chain().focus().setTextAlign("left").run()
								}
								disabled={!editor}
							>
								<AlignLeft className="h-4 w-4" />
							</Toggle>
							<Toggle
								variant="outline"
								size="sm"
								pressed={editor?.isActive({ textAlign: "center" }) ?? false}
								onPressedChange={() =>
									editor?.chain().focus().setTextAlign("center").run()
								}
								disabled={!editor}
							>
								<AlignCenter className="h-4 w-4" />
							</Toggle>
							<Toggle
								variant="outline"
								size="sm"
								pressed={editor?.isActive({ textAlign: "right" }) ?? false}
								onPressedChange={() =>
									editor?.chain().focus().setTextAlign("right").run()
								}
								disabled={!editor}
							>
								<AlignRight className="h-4 w-4" />
							</Toggle>
							<Toggle
								variant="outline"
								size="sm"
								pressed={editor?.isActive({ textAlign: "justify" }) ?? false}
								onPressedChange={() =>
									editor?.chain().focus().setTextAlign("justify").run()
								}
								disabled={!editor}
							>
								<AlignJustify className="h-4 w-4" />
							</Toggle>
						</div>
					</div>

					<div className="space-y-2">
						<div className="text-sm font-medium">Vertikale Ausrichtung</div>
						<div className="grid grid-cols-3 gap-1">
							<Toggle
								variant="outline"
								size="sm"
								pressed={
									editor?.getAttributes("textStyle")?.verticalAlign === "top" ||
									(!editor && selectedElement?.verticalAlign === "top")
								}
								onPressedChange={() => {
									if (editor) {
										// If already pressed, remove the alignment
										if (
											editor.getAttributes("textStyle")?.verticalAlign === "top"
										) {
											editor.chain().focus().unsetMark("textStyle").run();
											const editorElement = editor.view.dom;
											editorElement.style.removeProperty("vertical-align");
											editorElement.classList.remove("vertical-align-top");
											// Reset to default flex-start
											editorElement.style.justifyContent = "flex-start";

											// Remove any wrapper div with vertical-align style
											const html = editor.getHTML();
											if (html.startsWith('<div style="vertical-align:')) {
												const innerContentMatch = html.match(
													/<div[^>]*>([\s\S]*)<\/div>/,
												);
												if (innerContentMatch?.[1]) {
													editor.commands.setContent(innerContentMatch[1]);
												}
											}

											// Update the element's verticalAlign property
											if (selectedElement && onUpdateElement) {
												onUpdateElement({
													...selectedElement,
													verticalAlign: undefined,
												});
											}
										} else {
											// Otherwise, set the alignment to top
											// Check if there's a selection, if not, select all
											const { from, to } = editor.state.selection;
											if (from === to) {
												// No selection, select all content
												editor
													.chain()
													.focus()
													.selectAll()
													.setMark("textStyle", { verticalAlign: "top" })
													.run();
												// Clear selection after applying
												editor.commands.setTextSelection(0);
											} else {
												// Apply to selection
												editor
													.chain()
													.focus()
													.setMark("textStyle", { verticalAlign: "top" })
													.run();
											}

											// Apply to the editor element for immediate visual feedback
											const editorElement = editor.view.dom;
											editorElement.style.verticalAlign = "top";
											editorElement.classList.remove(
												"vertical-align-middle",
												"vertical-align-bottom",
											);
											editorElement.classList.add("vertical-align-top");
											editorElement.style.justifyContent = "flex-start";

											// Update the element's verticalAlign property
											if (selectedElement && onUpdateElement) {
												onUpdateElement({
													...selectedElement,
													verticalAlign: "top",
												});
											}
										}
									} else if (selectedElement && onUpdateElement) {
										// If no editor (not in edit mode), just update the element property
										onUpdateElement({
											...selectedElement,
											verticalAlign:
												selectedElement.verticalAlign === "top"
													? undefined
													: "top",
										});
									}
								}}
								disabled={!editor && !selectedElement}
							>
								<AlignVerticalJustifyStart className="h-4 w-4" />
							</Toggle>
							<Toggle
								variant="outline"
								size="sm"
								pressed={
									editor?.getAttributes("textStyle")?.verticalAlign ===
										"middle" ||
									(!editor && selectedElement?.verticalAlign === "middle")
								}
								onPressedChange={() => {
									if (editor) {
										// If already pressed, remove the alignment
										if (
											editor.getAttributes("textStyle")?.verticalAlign ===
											"middle"
										) {
											editor.chain().focus().unsetMark("textStyle").run();
											const editorElement = editor.view.dom;
											editorElement.style.removeProperty("vertical-align");
											editorElement.classList.remove("vertical-align-middle");
											// Reset to default flex-start
											editorElement.style.justifyContent = "flex-start";

											// Remove any wrapper div with vertical-align style
											const html = editor.getHTML();
											if (html.startsWith('<div style="vertical-align:')) {
												const innerContentMatch = html.match(
													/<div[^>]*>([\s\S]*)<\/div>/,
												);
												if (innerContentMatch?.[1]) {
													editor.commands.setContent(innerContentMatch[1]);
												}
											}

											// Update the element's verticalAlign property
											if (selectedElement && onUpdateElement) {
												onUpdateElement({
													...selectedElement,
													verticalAlign: undefined,
												});
											}
										} else {
											// Otherwise, set the alignment to middle
											// Check if there's a selection, if not, select all
											const { from, to } = editor.state.selection;
											if (from === to) {
												// No selection, select all content
												editor
													.chain()
													.focus()
													.selectAll()
													.setMark("textStyle", { verticalAlign: "middle" })
													.run();
												// Clear selection after applying
												editor.commands.setTextSelection(0);
											} else {
												// Apply to selection
												editor
													.chain()
													.focus()
													.setMark("textStyle", { verticalAlign: "middle" })
													.run();
											}

											// Apply to the editor element for immediate visual feedback
											const editorElement = editor.view.dom;
											editorElement.style.verticalAlign = "middle";
											editorElement.classList.remove(
												"vertical-align-top",
												"vertical-align-bottom",
											);
											editorElement.classList.add("vertical-align-middle");
											editorElement.style.justifyContent = "center";

											// Update the element's verticalAlign property
											if (selectedElement && onUpdateElement) {
												onUpdateElement({
													...selectedElement,
													verticalAlign: "middle",
												});
											}
										}
									} else if (selectedElement && onUpdateElement) {
										// If no editor (not in edit mode), just update the element property
										onUpdateElement({
											...selectedElement,
											verticalAlign:
												selectedElement.verticalAlign === "middle"
													? undefined
													: "middle",
										});
									}
								}}
								disabled={!editor && !selectedElement}
							>
								<AlignVerticalJustifyCenter className="h-4 w-4" />
							</Toggle>
							<Toggle
								variant="outline"
								size="sm"
								pressed={
									editor?.getAttributes("textStyle")?.verticalAlign ===
										"bottom" ||
									(!editor && selectedElement?.verticalAlign === "bottom")
								}
								onPressedChange={() => {
									if (editor) {
										// If already pressed, remove the alignment
										if (
											editor.getAttributes("textStyle")?.verticalAlign ===
											"bottom"
										) {
											editor.chain().focus().unsetMark("textStyle").run();
											const editorElement = editor.view.dom;
											editorElement.style.removeProperty("vertical-align");
											editorElement.classList.remove("vertical-align-bottom");
											// Reset to default flex-start
											editorElement.style.justifyContent = "flex-start";

											// Remove any wrapper div with vertical-align style
											const html = editor.getHTML();
											if (html.startsWith('<div style="vertical-align:')) {
												const innerContentMatch = html.match(
													/<div[^>]*>([\s\S]*)<\/div>/,
												);
												if (innerContentMatch?.[1]) {
													editor.commands.setContent(innerContentMatch[1]);
												}
											}

											// Update the element's verticalAlign property
											if (selectedElement && onUpdateElement) {
												onUpdateElement({
													...selectedElement,
													verticalAlign: undefined,
												});
											}
										} else {
											// Otherwise, set the alignment to bottom
											editor
												.chain()
												.focus()
												.setMark("textStyle", { verticalAlign: "bottom" })
												.run();
											// Apply to the editor element for immediate visual feedback
											const editorElement = editor.view.dom;
											editorElement.style.verticalAlign = "bottom";
											editorElement.classList.remove(
												"vertical-align-top",
												"vertical-align-middle",
											);
											editorElement.classList.add("vertical-align-bottom");
											editorElement.style.justifyContent = "flex-end";

											// Update the element's verticalAlign property
											if (selectedElement && onUpdateElement) {
												onUpdateElement({
													...selectedElement,
													verticalAlign: "bottom",
												});
											}
										}
									} else if (selectedElement && onUpdateElement) {
										// If no editor (not in edit mode), just update the element property
										onUpdateElement({
											...selectedElement,
											verticalAlign:
												selectedElement.verticalAlign === "bottom"
													? undefined
													: "bottom",
										});
									}
								}}
								disabled={!editor && !selectedElement}
							>
								<AlignVerticalJustifyEnd className="h-4 w-4" />
							</Toggle>
						</div>
					</div>

					<div className="space-y-2">
						<div className="text-sm font-medium">Listen & Einrückung</div>
						<div className="grid grid-cols-4 gap-1">
							<Button
								variant="outline"
								size="sm"
								onClick={() => editor?.chain().focus().decreaseIndent().run()}
								disabled={
									!editor ||
									(editor?.getAttributes("paragraph")?.indent || 0) === 0
								}
								title="Ausrücken (Shift+Tab)"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="16"
									height="16"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									strokeWidth="2"
									strokeLinecap="round"
									strokeLinejoin="round"
									className="lucide lucide-outdent"
									aria-hidden="true"
								>
									<title>Ausrücken</title>
									<polyline points="7 8 3 12 7 16" />
									<line x1="21" x2="3" y1="12" y2="12" />
									<line x1="21" x2="11" y1="6" y2="6" />
									<line x1="21" x2="11" y1="18" y2="18" />
								</svg>
							</Button>
							<Button
								variant="outline"
								size="sm"
								onClick={() => editor?.chain().focus().increaseIndent().run()}
								disabled={
									!editor ||
									(editor?.getAttributes("paragraph")?.indent || 0) >= 5
								}
								title="Einrücken (Tab)"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="16"
									height="16"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									strokeWidth="2"
									strokeLinecap="round"
									strokeLinejoin="round"
									className="lucide lucide-indent"
									aria-hidden="true"
								>
									<title>Einrücken</title>
									<polyline points="17 8 21 12 17 16" />
									<line x1="3" x2="21" y1="12" y2="12" />
									<line x1="3" x2="13" y1="6" y2="6" />
									<line x1="3" x2="13" y1="18" y2="18" />
								</svg>
							</Button>
							<Toggle
								variant="outline"
								size="sm"
								pressed={isBulletActive}
								onPressedChange={() =>
									editor?.chain().focus().toggleBulletList().run()
								}
								disabled={!editor}
							>
								<List className="h-4 w-4" />
							</Toggle>
							<Toggle
								variant="outline"
								size="sm"
								pressed={isNumberActive}
								onPressedChange={() =>
									editor?.chain().focus().toggleNumberedList().run()
								}
								disabled={!editor}
							>
								<ListOrdered className="h-4 w-4" />
							</Toggle>
						</div>
					</div>

					<div className="space-y-2">
						<div className="text-sm font-medium">Schriftgröße</div>
						<FontSizePicker editor={editor} selectedElement={selectedElement} />
					</div>

					<div className="space-y-2">
						<div className="text-sm font-medium">Zeilenhöhe</div>
						<LineHeightPicker editor={editor} />
					</div>

					<div className="space-y-2">
						<div className="text-sm font-medium">Buchstabenabstand</div>
						<LetterSpacingPicker editor={editor} />
					</div>

					<div className="space-y-2">
						<div className="text-sm font-medium">Schriftart</div>
						<FontPicker
							editor={editor}
							apiFonts={apiFonts}
							isLoading={isLoadingFonts}
							loadedFonts={loadedFonts}
							determineWeight={determineWeight}
						/>
					</div>

					<ColorPicker
						editor={editor}
						label="Textfarbe"
						apiColors={apiColors}
						isLoadingColors={isLoadingColors}
						colorError={colorError}
					/>

					<div className="space-y-2 mt-4">
						<div className="text-sm font-medium">Variablen</div>
						<div className="relative">
							<div className="relative">
								<Input
									type="text"
									placeholder="Suchen..."
									value={variableSearch}
									onChange={(e) => setVariableSearch(e.target.value)}
									className="pl-8"
								/>
								<Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
							</div>
						</div>
						<div className="max-h-60 overflow-y-auto border rounded-md">
							{filteredVariables.length > 0 ? (
								<div className="flex flex-col">
									{filteredVariables.map((variable) => (
										<button
											type="button"
											key={variable}
											className="text-left px-3 py-2 text-sm hover:bg-gray-100 border-b border-gray-100"
											onClick={() => insertVariable(variable)}
											disabled={!editor}
										>
											{variable}
										</button>
									))}
								</div>
							) : (
								<div className="p-3 text-sm text-gray-500">
									{documentVariables.length === 0
										? "Keine Variablen verfügbar"
										: "Keine Ergebnisse gefunden"}
								</div>
							)}
						</div>
					</div>
				</TabsContent>

				<TabsContent value="position" className="space-y-4 mt-2">
					<PositionSizeSettings
						element={selectedElement || null}
						onUpdate={handleUpdateElement}
					/>
				</TabsContent>
			</Tabs>
		</div>
	);
}

function FontPicker({
	editor,
	apiFonts,
	isLoading,
	loadedFonts,
	determineWeight,
}: FontPickerProps) {
	const [selectedFont, setSelectedFont] = useState<string>("");

	// Fallback fonts in case API call fails
	const fallbackFonts = useMemo(
		() => [
			{ name: "Arial", value: "Arial, sans-serif" },
			{ name: "Times New Roman", value: "Times New Roman, serif" },
			{ name: "Courier New", value: "Courier New, monospace" },
			{ name: "Georgia", value: "Georgia, serif" },
			{ name: "Verdana", value: "Verdana, sans-serif" },
		],
		[],
	);

	// Create font options array from API fonts or fallback fonts
	const fonts = useMemo(() => {
		if (apiFonts.length > 0) {
			return apiFonts.map((font) => {
				return {
					name: font.name,
					value: font.family,
					isLoaded: loadedFonts.has(font.family) || !font.path, // System fonts don't need loading
					isSystem: !font.path,
					styleInfo: font.style,
				};
			});
		}
		return fallbackFonts.map((font) => ({
			...font,
			isLoaded: true,
			isSystem: true,
			styleInfo: "Regular",
		}));
	}, [apiFonts, loadedFonts, fallbackFonts]);

	// Update selectedFont when editor selection changes
	useEffect(() => {
		if (editor) {
			const currentFontFamily = editor.getAttributes("textStyle")?.fontFamily;
			if (currentFontFamily) {
				setSelectedFont(currentFontFamily);
			} else {
				// If no font is selected, default to NeoSansforeprimo-Regular
				const defaultFont = "NeoSansforeprimo-Regular";
				setSelectedFont(defaultFont);
				// Also apply it to the editor instance
				editor
					?.chain()
					.focus()
					.setMark("textStyle", { fontFamily: defaultFont })
					.run();
			}
		}
	}, [editor, editor?.state.selection]);

	return (
		<Select
			value={selectedFont}
			onValueChange={(value) => {
				const selectedFontObject = fonts.find((f) => f.value === value);
				if (selectedFontObject) {
					setSelectedFont(value);
					console.log(`Setting font family: '${value}'`);
					editor
						?.chain()
						.focus()
						.setMark("textStyle", { fontFamily: value })
						.run();
					console.log(`Applied font: ${value}`);
				}
			}}
			disabled={!editor || isLoading}
		>
			<SelectTrigger className="w-full">
				<SelectValue
					placeholder={isLoading ? "Laden..." : "Schriftart wählen"}
				/>
			</SelectTrigger>
			<SelectContent>
				{isLoading ? (
					<div className="flex items-center justify-center p-2">
						<Loader2 className="h-4 w-4 animate-spin mr-2" />
						<span>Laden...</span>
					</div>
				) : (
					fonts.map((font) => (
						<SelectItem
							key={`${font.value}-${font.styleInfo || "regular"}`}
							value={font.value}
						>
							<span
								style={{
									fontFamily: font.isLoaded
										? `'${font.value}', sans-serif`
										: "inherit",
									fontWeight: determineWeight(font.styleInfo),
									fontStyle: font.styleInfo?.toLowerCase().includes("italic")
										? "italic"
										: "normal",
								}}
							>
								{font.name}
							</span>
						</SelectItem>
					))
				)}
			</SelectContent>
		</Select>
	);
}

function FontSizePicker({ editor, selectedElement }: FontSizePickerProps) {
	// Initial default font size - will be updated based on element type
	const initialDefaultSize = selectedElement?.isAddressField ? 9.5 : 8;
	const [fontSize, setFontSize] = useState<number>(initialDefaultSize);
	const [inputValue, setInputValue] = useState<string>(fontSize.toString());

	// Update state when editor selection changes
	useEffect(() => {
		if (editor) {
			const currentFontSize = editor.getAttributes("textStyle")?.fontSize;
			if (currentFontSize) {
				const size = parseFloat(currentFontSize);
				if (!Number.isNaN(size)) {
					setFontSize(size);
					setInputValue(size.toFixed(size % 1 === 0 ? 0 : 1));
				}
			} else {
				// Use the appropriate default font size based on element type
				const defaultSize = selectedElement?.isAddressField ? 9.5 : 8;
				setFontSize(defaultSize);
				setInputValue(defaultSize.toFixed(defaultSize % 1 === 0 ? 0 : 1));

				// Only apply font size in very specific circumstances to avoid conflicts
				const editorHtml = editor.getHTML();
				const isCompletelyEmpty = editorHtml === "<p></p>";

				// Only apply if content is completely empty AND no styling exists
				// This prevents conflicts with RichTextEditor's own font size preservation
				if (isCompletelyEmpty && !editorHtml.includes("style=")) {
					// Apply default font size to the editor
					editor
						?.chain()
						.focus()
						.setMark("textStyle", { fontSize: `${defaultSize}pt` })
						.run();
				}
			}
		}
	}, [editor, editor?.state.selection, selectedElement?.isAddressField]);

	const increaseFontSize = () => {
		const newSize = Math.min(72, fontSize + 0.5);
		setFontSize(newSize);
		setInputValue(newSize.toFixed(newSize % 1 === 0 ? 0 : 1));
		editor
			?.chain()
			.focus()
			.setMark("textStyle", { fontSize: `${newSize}pt` })
			.run();
	};

	const decreaseFontSize = () => {
		const newSize = Math.max(6, fontSize - 0.5);
		setFontSize(newSize);
		setInputValue(newSize.toFixed(newSize % 1 === 0 ? 0 : 1));
		editor
			?.chain()
			.focus()
			.setMark("textStyle", { fontSize: `${newSize}pt` })
			.run();
	};

	const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
		setInputValue(e.target.value);
	};

	const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter") {
			// Trigger blur to apply changes and refocus editor
			(e.target as HTMLInputElement).blur();
		}
	};

	const handleBlur = (e: FocusEvent<HTMLInputElement>) => {
		const valueStr = e.target.value;
		let newSize = fontSize;
		let newInputValue = valueStr;
		let applyUpdate = false;

		if (valueStr === "") {
			// Use appropriate default based on element type
			newSize = selectedElement?.isAddressField ? 9.5 : 6;
			newInputValue = newSize.toFixed(newSize % 1 === 0 ? 0 : 1);
			applyUpdate = true;
		} else {
			const value = parseFloat(valueStr);
			if (!Number.isNaN(value)) {
				if (value >= 6 && value <= 72) {
					newSize = value;
					newInputValue = value.toFixed(value % 1 === 0 ? 0 : 1);
					applyUpdate = true;
				} else if (value < 6) {
					newSize = 6;
					newInputValue = "6";
					applyUpdate = true;
				} else {
					newSize = 72;
					newInputValue = "72";
					applyUpdate = true;
				}
			} else {
				newInputValue = fontSize.toFixed(fontSize % 1 === 0 ? 0 : 1);
			}
		}

		setInputValue(newInputValue);
		const formattedNewSize = parseFloat(newSize.toFixed(1));
		const formattedCurrentFontSize = parseFloat(fontSize.toFixed(1));

		if (applyUpdate && formattedNewSize !== formattedCurrentFontSize) {
			setFontSize(formattedNewSize);
			editor
				?.chain()
				.focus()
				.setMark("textStyle", { fontSize: `${formattedNewSize}pt` })
				.run();
		} else if (
			applyUpdate &&
			formattedNewSize === formattedCurrentFontSize &&
			valueStr === ""
		) {
			editor
				?.chain()
				.focus()
				.setMark("textStyle", { fontSize: `${formattedNewSize}pt` })
				.run();
		} else if (!applyUpdate) {
			setInputValue(fontSize.toFixed(fontSize % 1 === 0 ? 0 : 1));
		}
	};

	return (
		<div className="flex items-center">
			<Button
				variant="outline"
				size="icon"
				className="h-9 w-9"
				onClick={decreaseFontSize}
				disabled={!editor || fontSize <= 6}
			>
				<Minus className="h-4 w-4" />
			</Button>

			<input
				type="number"
				value={inputValue}
				onChange={handleInputChange}
				onKeyDown={handleKeyDown}
				onBlur={handleBlur}
				className="h-9 w-14 mx-1 text-center border border-input rounded-md"
				min="6"
				max="72"
				step="0.5"
				disabled={!editor}
			/>

			<Button
				variant="outline"
				size="icon"
				className="h-9 w-9"
				onClick={increaseFontSize}
				disabled={!editor || fontSize >= 72}
			>
				<Plus className="h-4 w-4" />
			</Button>
		</div>
	);
}

function LineHeightPicker({ editor }: LineHeightPickerProps) {
	const [lineHeight, setLineHeight] = useState<number>(1.2); // Initial default as factor
	const [inputValue, setInputValue] = useState<string>(lineHeight.toFixed(1));

	// Update state when editor selection changes
	useEffect(() => {
		if (editor) {
			// Get current line height
			const currentLineHeight = editor.getAttributes("textStyle")?.lineHeight;
			if (currentLineHeight) {
				// Check if it's in pt format (legacy)
				if (currentLineHeight.endsWith("pt")) {
					// Convert pt to factor based on current font size
					const heightPt = parseFloat(currentLineHeight);
					const currentFontSizeAttr =
						editor.getAttributes("textStyle")?.fontSize;
					let fontSizeValue = 8; // Default font size if none is set

					if (currentFontSizeAttr) {
						const match = currentFontSizeAttr.match(/^(\d+(\.\d+)?)/);
						if (match) {
							fontSizeValue = parseFloat(match[1]);
						}
					}

					if (!Number.isNaN(heightPt) && fontSizeValue > 0) {
						const factor = heightPt / fontSizeValue;
						setLineHeight(factor);
						setInputValue(factor.toFixed(1));
					}
				} else {
					// Handle unitless line height (already a factor)
					const heightFactor = parseFloat(currentLineHeight);
					if (!Number.isNaN(heightFactor)) {
						setLineHeight(heightFactor);
						setInputValue(heightFactor.toFixed(1));
					}
				}
			} else {
				// Default line height factor
				setLineHeight(1.2);
				setInputValue("1.2");
			}
		}
	}, [editor, editor?.state.selection]);

	const increaseLineHeight = () => {
		const newHeight = Math.min(3.0, lineHeight + 0.1); // Max 3.0 factor
		setLineHeight(newHeight);
		setInputValue(newHeight.toFixed(1));
		editor
			?.chain()
			.focus()
			.setMark("textStyle", { lineHeight: newHeight.toString() })
			.run();
	};

	const decreaseLineHeight = () => {
		const newHeight = Math.max(0.8, lineHeight - 0.1); // Min 0.8 factor
		setLineHeight(newHeight);
		setInputValue(newHeight.toFixed(1));
		editor
			?.chain()
			.focus()
			.setMark("textStyle", { lineHeight: newHeight.toString() })
			.run();
	};

	const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
		setInputValue(e.target.value);
	};

	const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter") {
			// Trigger blur to apply changes and refocus editor
			(e.target as HTMLInputElement).blur();
		}
	};

	const handleBlur = (e: FocusEvent<HTMLInputElement>) => {
		const valueStr = e.target.value;
		let newHeight = lineHeight;
		let newInputValue = valueStr;
		let applyUpdate = false;

		if (valueStr === "") {
			// Use default factor
			newHeight = 1.2;
			newInputValue = "1.2";
			applyUpdate = true;
		} else {
			const value = parseFloat(valueStr);
			if (!Number.isNaN(value)) {
				if (value >= 0.8 && value <= 3.0) {
					newHeight = value;
					newInputValue = value.toFixed(1);
					applyUpdate = true;
				} else if (value < 0.8) {
					newHeight = 0.8;
					newInputValue = "0.8";
					applyUpdate = true;
				} else {
					// value > 3.0
					newHeight = 3.0;
					newInputValue = "3.0";
					applyUpdate = true;
				}
			} else {
				newInputValue = lineHeight.toFixed(1);
			}
		}

		setInputValue(newInputValue);
		if (applyUpdate && Math.abs(newHeight - lineHeight) > 0.01) {
			setLineHeight(newHeight);
			editor
				?.chain()
				.focus()
				.setMark("textStyle", { lineHeight: newHeight.toString() })
				.run();
		} else if (
			applyUpdate &&
			Math.abs(newHeight - lineHeight) <= 0.01 &&
			valueStr === ""
		) {
			editor
				?.chain()
				.focus()
				.setMark("textStyle", { lineHeight: newHeight.toString() })
				.run();
		} else if (!applyUpdate) {
			setInputValue(lineHeight.toFixed(1));
		}
	};

	return (
		<div className="flex items-center">
			<Button
				variant="outline"
				size="icon"
				className="h-9 w-9"
				onClick={decreaseLineHeight}
				disabled={!editor || lineHeight <= 0.8}
			>
				<Minus className="h-4 w-4" />
			</Button>

			<div className="relative flex items-center">
				<input
					type="number"
					value={inputValue}
					onChange={handleInputChange}
					onKeyDown={handleKeyDown}
					onBlur={handleBlur}
					className="h-9 w-14 mx-1 text-center border border-input rounded-md"
					min="0.8"
					max="3.0"
					step="0.1"
					disabled={!editor}
				/>
			</div>

			<Button
				variant="outline"
				size="icon"
				className="h-9 w-9"
				onClick={increaseLineHeight}
				disabled={!editor || lineHeight >= 3.0}
			>
				<Plus className="h-4 w-4" />
			</Button>
		</div>
	);
}

function LetterSpacingPicker({ editor }: LetterSpacingPickerProps) {
	const [letterSpacing, setLetterSpacing] = useState<number>(0);
	const [inputValue, setInputValue] = useState<string>(
		letterSpacing.toString(),
	);

	// Update state when editor selection changes
	useEffect(() => {
		if (editor) {
			const currentLetterSpacing =
				editor.getAttributes("textStyle")?.letterSpacing;
			if (currentLetterSpacing) {
				const spacing = parseInt(currentLetterSpacing);
				if (!Number.isNaN(spacing)) {
					setLetterSpacing(spacing);
					setInputValue(spacing.toString());
				}
			} else {
				// Reset to default if no letter spacing is set
				setLetterSpacing(0);
				setInputValue("0");
			}
		}
	}, [editor, editor?.state.selection]);

	const increaseLetterSpacing = () => {
		const newSpacing = Math.min(10, letterSpacing + 1);
		setLetterSpacing(newSpacing);
		setInputValue(newSpacing.toString());
		editor
			?.chain()
			.focus()
			.setMark("textStyle", { letterSpacing: `${newSpacing}px` })
			.run();
	};

	const decreaseLetterSpacing = () => {
		const newSpacing = Math.max(-2, letterSpacing - 1);
		setLetterSpacing(newSpacing);
		setInputValue(newSpacing.toString());
		editor
			?.chain()
			.focus()
			.setMark("textStyle", { letterSpacing: `${newSpacing}px` })
			.run();
	};

	const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
		setInputValue(e.target.value);
	};

	const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter") {
			// Trigger blur to apply changes and refocus editor
			(e.target as HTMLInputElement).blur();
		}
	};

	const handleBlur = (e: FocusEvent<HTMLInputElement>) => {
		const valueStr = e.target.value;
		let newSpacing = letterSpacing;
		let newInputValue = valueStr;
		let applyUpdate = false;

		if (valueStr === "") {
			newSpacing = -2;
			newInputValue = "-2";
			applyUpdate = true;
		} else {
			const value = parseInt(valueStr);
			if (!Number.isNaN(value)) {
				if (value >= -2 && value <= 10) {
					newSpacing = value;
					newInputValue = value.toString();
					applyUpdate = true;
				} else if (value < -2) {
					newSpacing = -2;
					newInputValue = "-2";
					applyUpdate = true;
				} else {
					// value > 10
					newSpacing = 10;
					newInputValue = "10";
					applyUpdate = true;
				}
			} else {
				newInputValue = letterSpacing.toString();
			}
		}

		setInputValue(newInputValue);
		if (applyUpdate && newSpacing !== letterSpacing) {
			setLetterSpacing(newSpacing);
			editor
				?.chain()
				.focus()
				.setMark("textStyle", { letterSpacing: `${newSpacing}px` })
				.run();
		} else if (applyUpdate && newSpacing === letterSpacing && valueStr === "") {
			editor
				?.chain()
				.focus()
				.setMark("textStyle", { letterSpacing: `${newSpacing}px` })
				.run();
		} else if (!applyUpdate) {
			setInputValue(letterSpacing.toString());
		}
	};

	return (
		<div className="flex items-center">
			<Button
				variant="outline"
				size="icon"
				className="h-9 w-9"
				onClick={decreaseLetterSpacing}
				disabled={!editor || letterSpacing <= -2}
			>
				<Minus className="h-4 w-4" />
			</Button>

			<input
				type="number"
				value={inputValue}
				onChange={handleInputChange}
				onKeyDown={handleKeyDown}
				onBlur={handleBlur}
				className="h-9 w-14 mx-1 text-center border border-input rounded-md"
				min="-2"
				max="10"
				step="1"
				disabled={!editor}
			/>

			<Button
				variant="outline"
				size="icon"
				className="h-9 w-9"
				onClick={increaseLetterSpacing}
				disabled={!editor || letterSpacing >= 10}
			>
				<Plus className="h-4 w-4" />
			</Button>
		</div>
	);
}
