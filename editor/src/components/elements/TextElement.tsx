import type { Editor } from "@tiptap/react";
import type { Element } from "../../types/element";
import { getTextElementStyles } from "../../utils/elementStyles";
import { RichTextEditor } from "../RichTextEditor/RichTextEditor";

interface TextElementProps {
	element: Element;
	isEditing: boolean;
	contentToDisplay: string;
	isExportMode: boolean;
	showElementBorders: boolean;
	onChange: (content: string) => void;
	setActiveEditor: (editor: Editor | null) => void;
	setIsTextEditorFocused?: (focused: boolean) => void;
}

export function TextElement({
	element,
	isEditing,
	contentToDisplay,
	isExportMode,
	showElementBorders,
	onChange,
	setActiveEditor,
	setIsTextEditorFocused,
}: TextElementProps) {
	let defaultFontSize = "8pt";
	if (element.isAddressField) {
		defaultFontSize = "9.5pt";
	}

	if (isEditing) {
		if (!setIsTextEditorFocused) {
			throw new Error("setIsTextEditorFocused is required");
		}

		return (
			<RichTextEditor
				content={element.content || ""}
				onChange={onChange}
				setActiveEditor={setActiveEditor}
				verticalAlign={element.verticalAlign}
				setIsTextEditorFocused={setIsTextEditorFocused}
				defaultFontSize={defaultFontSize}
			/>
		);
	}

	// Use the element's verticalAlign property if available
	// Otherwise, try to extract it from the content as a fallback
	const verticalAlign =
		element.verticalAlign ||
		(() => {
			// Fallback: try to extract from content
			const verticalAlignMatch = element.content?.match(
				/vertical-align:\s*(top|middle|bottom)/,
			);
			if (verticalAlignMatch?.[1]) {
				return verticalAlignMatch[1] as "top" | "middle" | "bottom";
			}
			return undefined;
		})();

	// If the content has a wrapper div with vertical-align, extract the inner content
	let finalHtml = contentToDisplay;
	if (finalHtml.startsWith('<div style="vertical-align:')) {
		const innerContentMatch = finalHtml.match(/<div[^>]*>([\s\S]*)<\/div>/);
		if (innerContentMatch?.[1]) {
			finalHtml = innerContentMatch[1];
		}
	}

	const textStyles = getTextElementStyles(verticalAlign, isExportMode);

	// Define consistent border width
	const borderWidth = 1;

	// Determine if we should show borders
	const shouldShowBorder = !isExportMode && showElementBorders;

	return (
		<div
			className={`ProseMirror w-full h-full ${shouldShowBorder ? "border border-gray-300" : ""} break-words whitespace-pre-wrap overflow-hidden ${verticalAlign ? `vertical-align-${verticalAlign}` : ""}`}
			data-element-content
			data-address-field={element.isAddressField ? "true" : undefined}
			style={{
				...textStyles,
				// Maintain consistent spacing by always using the same total space (border + padding = borderWidth)
				padding: shouldShowBorder ? "0px" : `${borderWidth}px`,
				boxSizing: "border-box",
				// Allow events to pass through to parent when not editing, but enable them when editing
				pointerEvents: isEditing ? "auto" : "none",
			}}
			// biome-ignore lint/security/noDangerouslySetInnerHtml: we need this
			dangerouslySetInnerHTML={{
				__html: finalHtml,
			}}
		/>
	);
}
