import { render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";

import type { Element } from "@/types/element";
import { TextElement } from "../TextElement";

// Mock the RichTextEditor component since it's complex to test
vi.mock("../../RichTextEditor/RichTextEditor", () => ({
	RichTextEditor: () => <div data-testid="rich-text-editor">Editor Mock</div>,
}));

describe("TextElement component", () => {
	const mockElement: Element = {
		id: "text-1",
		currentPageId: "page-1",
		type: "text",
		x: 10,
		y: 20,
		width: 100,
		height: 50,
		rotation: 0,
		content: "<p>Test content</p>",
		verticalAlign: "middle",
	};

	const mockSetActiveEditor = vi.fn();
	const mockOnChange = vi.fn();
	const mockSetIsTextEditorFocused = vi.fn();

	it("renders text content in display mode", () => {
		render(
			<TextElement
				element={mockElement}
				isEditing={false}
				contentToDisplay={mockElement.content || ""}
				isExportMode={false}
				showElementBorders={true}
				onChange={mockOnChange}
				setActiveEditor={mockSetActiveEditor}
			/>,
		);

		const textContainer = screen.getByText("Test content");
		expect(textContainer).toBeInTheDocument();
		const container = textContainer.closest("[data-element-content]");
		expect(container).toHaveAttribute("style");
		// Check that the style attribute contains the pointer-events property
		expect(container?.getAttribute("style")).toContain("pointer-events: none");
	});

	it("renders RichTextEditor in editing mode", () => {
		render(
			<TextElement
				element={mockElement}
				isEditing={true}
				contentToDisplay={mockElement.content || ""}
				isExportMode={false}
				showElementBorders={true}
				onChange={mockOnChange}
				setActiveEditor={mockSetActiveEditor}
				setIsTextEditorFocused={mockSetIsTextEditorFocused}
			/>,
		);

		// Verify the mocked RichTextEditor component is rendered
		expect(screen.getByTestId("rich-text-editor")).toBeInTheDocument();
		expect(screen.getByText("Editor Mock")).toBeInTheDocument();
	});

	it("applies vertical alignment styles", () => {
		const { rerender } = render(
			<TextElement
				element={mockElement}
				isEditing={false}
				contentToDisplay={mockElement.content || ""}
				isExportMode={false}
				showElementBorders={true}
				onChange={mockOnChange}
				setActiveEditor={mockSetActiveEditor}
			/>,
		);

		// Check middle alignment
		let container = screen
			.getByText("Test content")
			.closest("[data-element-content]");
		expect(container?.getAttribute("style")).toContain(
			"justify-content: center",
		);

		// Check top alignment
		rerender(
			<TextElement
				element={{ ...mockElement, verticalAlign: "top" }}
				isEditing={false}
				contentToDisplay={mockElement.content || ""}
				isExportMode={false}
				showElementBorders={true}
				onChange={mockOnChange}
				setActiveEditor={mockSetActiveEditor}
			/>,
		);

		container = screen
			.getByText("Test content")
			.closest("[data-element-content]");
		expect(container?.getAttribute("style")).toContain(
			"justify-content: flex-start",
		);

		// Check bottom alignment
		rerender(
			<TextElement
				element={{ ...mockElement, verticalAlign: "bottom" }}
				isEditing={false}
				contentToDisplay={mockElement.content || ""}
				isExportMode={false}
				showElementBorders={true}
				onChange={mockOnChange}
				setActiveEditor={mockSetActiveEditor}
			/>,
		);

		container = screen
			.getByText("Test content")
			.closest("[data-element-content]");
		expect(container?.getAttribute("style")).toContain(
			"justify-content: flex-end",
		);
	});

	it("handles address field flag", () => {
		render(
			<TextElement
				element={{ ...mockElement, isAddressField: true }}
				isEditing={false}
				contentToDisplay={mockElement.content || ""}
				isExportMode={false}
				showElementBorders={true}
				onChange={mockOnChange}
				setActiveEditor={mockSetActiveEditor}
			/>,
		);

		const container = screen
			.getByText("Test content")
			.closest("[data-element-content]");
		expect(container).toHaveAttribute("data-address-field", "true");
	});

	it("handles export mode", () => {
		render(
			<TextElement
				element={mockElement}
				isEditing={false}
				contentToDisplay={mockElement.content || ""}
				isExportMode={true}
				showElementBorders={true}
				onChange={mockOnChange}
				setActiveEditor={mockSetActiveEditor}
			/>,
		);

		const container = screen
			.getByText("Test content")
			.closest("[data-element-content]");
		expect(container).not.toHaveClass("border");
		expect(container?.getAttribute("style")).toContain("padding: 1px");
	});

	it("extracts content from wrapper div if needed", () => {
		const wrappedContent =
			'<div style="vertical-align:middle"><p>Wrapped content</p></div>';

		render(
			<TextElement
				element={mockElement}
				isEditing={false}
				contentToDisplay={wrappedContent}
				isExportMode={false}
				showElementBorders={true}
				onChange={mockOnChange}
				setActiveEditor={mockSetActiveEditor}
			/>,
		);

		const textContainer = screen.getByText("Wrapped content");
		expect(textContainer).toBeInTheDocument();
	});
});
