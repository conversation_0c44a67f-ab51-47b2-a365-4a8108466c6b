import { render, screen } from "@testing-library/react";
import { describe, expect, it } from "vitest";

import type { Element } from "@/types/element";
import { ImageElement } from "../ImageElement";

describe("ImageElement component", () => {
	const mockImageElement: Element = {
		id: "image-1",
		currentPageId: "page-1",
		type: "image",
		x: 10,
		y: 20,
		width: 100,
		height: 50,
		rotation: 0,
		content: "data:image/png;base64,abc123", // Mock base64 image data
	};

	it("renders image from element content", () => {
		const { container } = render(<ImageElement element={mockImageElement} />);

		const img = container.querySelector("img");
		expect(img).toBeInTheDocument();
		expect(img).toHaveAttribute("src", "data:image/png;base64,abc123");
		expect(img).toHaveClass("object-contain");
		expect(img).toHaveAttribute("draggable", "false");
	});

	it("renders placeholder when no content is provided", () => {
		const elementWithoutContent = { ...mockImageElement, content: "" };
		const { container } = render(
			<ImageElement element={elementWithoutContent} />,
		);

		const placeholder = container.querySelector("div");
		expect(placeholder).toHaveTextContent("Kein Bild");
		expect(container.querySelector("img")).not.toBeInTheDocument();
	});

	it("renders placeholder when content is undefined", () => {
		const emptyElement = { ...mockImageElement, content: undefined };

		render(<ImageElement element={emptyElement} />);

		expect(screen.getByText("Kein Bild")).toBeInTheDocument();
	});

	it("applies border radius when specified", () => {
		const elementWithBorderRadius = {
			...mockImageElement,
			borderRadius: [5, 10, 15, 20],
		};

		const { container } = render(
			<ImageElement element={elementWithBorderRadius} />,
		);

		const wrapper = container.firstChild as HTMLElement;
		expect(wrapper.style.borderRadius).toBe("5mm 10mm 15mm 20mm");
		expect(wrapper.style.overflow).toBe("hidden");
	});

	it("shows placeholder when content is null", () => {
		const elementWithNullContent = { ...mockImageElement, content: undefined };
		render(<ImageElement element={elementWithNullContent} />);

		expect(screen.getByText("Kein Bild")).toBeInTheDocument();
		expect(screen.queryByRole("img")).not.toBeInTheDocument();
	});

	it("renders image with proper classes and attributes", () => {
		const { container } = render(<ImageElement element={mockImageElement} />);

		const img = container.querySelector("img");
		expect(img).toBeInTheDocument();
		expect(img).toHaveClass(
			"w-full",
			"h-full",
			"object-contain",
			"select-none",
			"pointer-events-none",
		);
		expect(img).toHaveAttribute("draggable", "false");
		expect(img).toHaveAttribute("alt", "");
	});
});
