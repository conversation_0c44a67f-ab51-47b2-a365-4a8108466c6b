import type { Editor } from "@tiptap/react";
import { useEffect, useRef, useState } from "react";
import { AddressElement } from "@/components/elements/AddressElement";
import { BlockElement } from "@/components/elements/BlockElement";
import { ControlElements } from "@/components/elements/ControlElements";
import { ImageElement } from "@/components/elements/ImageElement";
import { ShapeElement } from "@/components/elements/ShapeElement";
import { TableElement } from "@/components/elements/TableElement";
import { TextElement } from "@/components/elements/TextElement";
import { RichTextEditor } from "@/components/RichTextEditor/RichTextEditor";
import { TableEditor } from "@/components/table/TableEditor";
import { useElementEventHandlers } from "@/hooks/useElementEventHandlers";
import {
	type ResizeHandleType,
	useElementTransform,
} from "@/hooks/useElementTransform";
import type { Element } from "@/types/element";
import type { TestDataRecord } from "@/utils/apiService";
import { processContentWithVariables } from "@/utils/contentProcessing";
import {
	calculateElementOpacity,
	calculateElementZIndex,
	getElementCursorStyle,
	getElementInteractivity,
	getElementPointerEvents,
	getElementPositionStyles,
} from "@/utils/elementStyles";
import { getTableBorderContributions } from "@/utils/tableUtils";

// Define SnapGuideInfo if not already globally available (mirroring Page.tsx)
interface SnapGuideInfo {
	type: "horizontal" | "vertical";
	position: number;
	start: number;
	end: number;
}

interface EditableElementProps {
	element: Element;
	onUpdate: (updatedElement: Element, isFinal: boolean) => void;
	onDelete: (elementId: string) => void;
	onCopy: (elementId: string) => void;
	onCut: (elementId: string) => void;
	onSaveAsTemplate?: (blockElement: Element, childElements: Element[]) => void;
	isSelected: boolean;
	onSelect: (elementId: string) => void;
	setActiveEditor: (editor: Editor | null) => void;
	onEditingChange?: (isEditing: boolean) => void;
	testData?: TestDataRecord[];
	selectedTestDataIndex?: number;
	highlightVariables?: boolean;
	showElementBorders?: boolean;
	isExportMode?: boolean;
	previewMode?: boolean;
	zIndexOffset?: number;
	updateSnapGuides?: (
		visible: boolean,
		guides: SnapGuideInfo[],
		relativeToBlockId?: string | null,
		blockInfo?: { x: number; y: number; width: number; height: number } | null,
	) => void;
	isLocked?: boolean;
	childElements?: Element[];
	editingBlockId?: string | null;
	onEnterBlockEditMode?: (blockId: string | null) => void;
	globalSelectedElementId?: string | null;
	calculateSnapGuides?: (
		elementId: string,
		newX: number,
		newY: number,
		elementWidth: number,
		elementHeight: number,
		parentBlockId?: string | null,
		movingHandle?: ResizeHandleType,
	) => { snapX: number | null; snapY: number | null; guides: SnapGuideInfo[] };
	setIsTextEditorFocused?: (focused: boolean) => void;
}

export function EditableElement({
	element,
	onUpdate,
	onDelete,
	onCopy,
	onCut,
	onSaveAsTemplate,
	isSelected,
	onSelect,
	setActiveEditor,
	onEditingChange,
	testData = [],
	selectedTestDataIndex = -1,
	highlightVariables = false,
	showElementBorders = true,
	isExportMode = false,
	previewMode: _previewMode = true,
	zIndexOffset = 0,
	updateSnapGuides,
	isLocked = false,
	childElements,
	editingBlockId,
	onEnterBlockEditMode,
	globalSelectedElementId,
	calculateSnapGuides,
	setIsTextEditorFocused,
}: EditableElementProps) {
	// Use the transform hook for dragging and resizing
	const { handleDragStart, handleResizeStart } = useElementTransform({
		element,
		onUpdate,
		updateSnapGuides,
		editingBlockId,
		calculateSnapGuides,
	});

	const [isEditing, setIsEditing] = useState(false);
	const [contentToDisplay, setContentToDisplay] = useState<string>(
		element.content || "<p></p>",
	);

	// Image-specific state

	// Calculate border contributions for tables
	const {
		h: tableBorderH,
		v: tableBorderV,
		left: tableLeftShift,
		top: tableTopShift,
	} = element.type === "table"
		? getTableBorderContributions(element.tableProperties)
		: { h: 0, v: 0, left: 0, top: 0 };

	// Use event handlers hook
	const {
		handleTextEditStart,
		handleTextChange,
		handleTextEditEnd,
		handleTableEditStart,
		handleTableChange,
		handleTableEditEnd,
		handleKeyDown,
	} = useElementEventHandlers({
		element,
		onUpdate,
		onDelete,
		onCopy,
		onCut,
		_onSelect: onSelect,
		isSelected,
		isEditing,
		setIsEditing,
		isLocked,
	});

	// Effect to calculate final content based on test data and highlighting
	useEffect(() => {
		const finalContent = processContentWithVariables({
			content: element.content || "<p></p>",
			isEditing,
			highlightVariables,
			selectedTestDataIndex,
			testData,
		});

		setContentToDisplay(finalContent);
	}, [
		element.content,
		isEditing,
		selectedTestDataIndex,
		testData,
		highlightVariables,
	]);

	// Notify parent component when editing state changes
	useEffect(() => {
		onEditingChange?.(isEditing);
	}, [isEditing, onEditingChange]);

	const elementRef = useRef<HTMLDivElement>(null);

	const handleMouseDown = (e: React.MouseEvent) => {
		const target = e.target as HTMLElement;

		// Don't handle if clicking on a handle - let handleResizeStart manage it
		if (target.classList.contains("handle")) {
			return;
		}

		if (elementRef.current) {
			elementRef.current.focus();
		}

		// If locked, only allow selection, no editing or dragging
		if (isLocked) {
			onSelect(element.id);
			e.stopPropagation();
			return;
		}

		// Check for double click on text or table elements
		if (e.detail === 2) {
			e.stopPropagation();

			// First select the element to ensure it's active
			onSelect(element.id);

			if (element.type === "text") {
				handleTextEditStart();
			} else if (element.type === "table") {
				handleTableEditStart();
			}

			return;
		}

		// handle left clicks for dragging - only if element is not locked
		if (e.button === 0 && !element.isLocked) {
			handleDragStart(e);
			onSelect(element.id);
		} else {
			// For locked elements, just select them without enabling drag
			onSelect(element.id);
		}
		e.stopPropagation();
	};

	useEffect(() => {
		const handleClickOutside = (e: MouseEvent) => {
			const target = e.target as HTMLElement;

			// Check for clicks on protected UI areas
			const isToolbarClick = target.closest(".format-toolbar");
			const isTopBarClick = target.closest(".top-bar");
			const isSidebarClick =
				target.closest(".sidebar-container") ||
				target.closest(".sidebar-tab-content");
			const isSelectDropdown =
				target.closest('[role="listbox"]') ||
				target.closest("[data-radix-popper-content-wrapper]") ||
				target.closest('[role="dialog"]');

			// Also check for any editor-related UI elements
			const isEditorUI =
				target.closest(".ProseMirror") ||
				target.closest(".tiptap") ||
				target.closest(".tiptap-toolbar") ||
				target.closest(".tiptap-menu");

			// Don't close if clicking on toolbar, top bar, sidebar, dropdown, or editor UI
			if (
				isToolbarClick ||
				isTopBarClick ||
				isSidebarClick ||
				isSelectDropdown ||
				isEditorUI
			) {
				return;
			}

			if (
				elementRef.current &&
				!elementRef.current.contains(e.target as Node)
			) {
				// Exit edit mode when clicking outside
				if (isEditing) {
					// Commit the final state when exiting edit mode
					if (element.type === "text") handleTextEditEnd();
					else if (element.type === "table") handleTableEditEnd();
				}

				// Unselect the element if it's currently selected
				if (isSelected) {
					// Use setTimeout to avoid conflicts with other click handlers
					setTimeout(() => {
						// Dispatch a custom event that the Page component can listen for
						document.dispatchEvent(new CustomEvent("element:unselect"));
					}, 0);
				}
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, [
		isEditing,
		element.type,
		isSelected,
		handleTextEditEnd,
		handleTableEditEnd,
	]); // Added edit end handlers

	const renderElementContent = () => {
		switch (element.type) {
			case "text":
				return (
					<TextElement
						element={element}
						isEditing={isEditing}
						contentToDisplay={contentToDisplay}
						isExportMode={isExportMode}
						showElementBorders={showElementBorders}
						onChange={handleTextChange}
						setActiveEditor={setActiveEditor}
						setIsTextEditorFocused={setIsTextEditorFocused}
					/>
				);

			case "shape":
				return <ShapeElement element={element} />;

			case "image":
				return <ImageElement element={element} />;

			case "table":
				return (
					<TableElement
						element={element}
						testData={testData}
						selectedTestDataIndex={selectedTestDataIndex}
						highlightVariables={highlightVariables}
						onUpdate={onUpdate}
						onDelete={onDelete}
					/>
				);

			case "block":
				return (
					<BlockElement
						element={element}
						childElements={childElements}
						editingBlockId={editingBlockId}
						globalSelectedElementId={globalSelectedElementId}
						onEnterBlockEditMode={onEnterBlockEditMode}
						onUpdate={onUpdate}
						onDelete={onDelete}
						onCopy={onCopy}
						onCut={onCut}
						onSelect={onSelect}
						setActiveEditor={setActiveEditor}
						onEditingChange={onEditingChange}
						testData={testData}
						selectedTestDataIndex={selectedTestDataIndex}
						highlightVariables={highlightVariables}
						showElementBorders={showElementBorders}
						updateSnapGuides={updateSnapGuides}
						calculateSnapGuides={calculateSnapGuides}
						onParentKeyDown={handleKeyDown}
					/>
				);

			case "address":
				return <AddressElement />;

			default:
				return null;
		}
	};

	const renderControlElements = () => {
		return (
			<ControlElements
				element={element}
				isSelected={isSelected}
				isExportMode={isExportMode}
				isLocked={isLocked}
				editingBlockId={editingBlockId}
				onCopy={onCopy}
				onCut={onCut}
				onDelete={onDelete}
				onSaveAsTemplate={onSaveAsTemplate}
				onResizeStart={handleResizeStart}
				childElements={childElements}
			/>
		);
	};

	// Calculate element interactivity and styles using utility functions
	const {
		isGenerallyInteractive,
		disableSelfInteractionForBlockInEditMode,
		isAddressField,
	} = getElementInteractivity(element, editingBlockId || null);

	const finalPointerEvents = getElementPointerEvents(
		isExportMode,
		isLocked,
		isGenerallyInteractive,
		isAddressField,
	);
	const canMouseDown =
		!isExportMode &&
		!isEditing &&
		!isLocked &&
		isGenerallyInteractive &&
		!disableSelfInteractionForBlockInEditMode &&
		!isAddressField;
	const canKeyDown =
		!isExportMode &&
		!isEditing &&
		isGenerallyInteractive &&
		!disableSelfInteractionForBlockInEditMode &&
		!isAddressField;
	const cursorStyle = getElementCursorStyle(
		isLocked,
		element.isLocked || false,
		disableSelfInteractionForBlockInEditMode,
		isGenerallyInteractive,
		isAddressField,
	);

	// Calculate z-index and opacity
	const effectiveZIndex = calculateElementZIndex(
		element,
		isSelected,
		isEditing,
		zIndexOffset,
	);
	const elementOpacity = calculateElementOpacity(
		editingBlockId || null,
		isGenerallyInteractive,
	);

	// Get position styles
	const positionStyles = getElementPositionStyles(
		element,
		tableBorderH,
		tableBorderV,
		tableLeftShift,
		tableTopShift,
	);
	if (isEditing) {
		// Handle text elements with RichTextEditor
		if (element.type === "text") {
			if (!setIsTextEditorFocused) {
				throw new Error("setIsTextEditorFocused is required");
			}

			return (
				<div
					ref={elementRef}
					className="absolute overflow-hidden"
					style={{
						...positionStyles,
						touchAction: "none",
						position: "absolute",
						zIndex: effectiveZIndex,
						cursor: "text",
						overflow: "hidden",
						opacity: elementOpacity,
					}}
				>
					<div
						className="w-full h-full overflow-hidden"
						style={{
							boxSizing: "border-box",
							border: "1px solid rgb(209 213 219)", // border-gray-300 equivalent
						}}
					>
						<RichTextEditor
							content={element.content || ""}
							onChange={handleTextChange}
							setActiveEditor={setActiveEditor}
							verticalAlign={element.verticalAlign}
							setIsTextEditorFocused={setIsTextEditorFocused}
							defaultFontSize={element.isAddressField ? "9.5pt" : "8pt"}
						/>
					</div>
				</div>
			);
		}

		// Handle table elements with TableEditor
		if (element.type === "table" && element.tableProperties) {
			if (!setIsTextEditorFocused) {
				throw new Error("setIsTextEditorFocused is required");
			}

			return (
				<div
					ref={elementRef}
					className="absolute"
					style={{
						...positionStyles,
						touchAction: "none",
						position: "absolute",
						zIndex: effectiveZIndex,
					}}
				>
					<TableEditor
						// Pass the potentially scaled properties from the onUpdate call in handleTableEditStart
						tableProperties={element.tableProperties}
						onChange={handleTableChange}
						setActiveEditor={setActiveEditor}
						containerWidthMm={element.width}
						containerHeightMm={element.height}
						testData={testData}
						selectedTestDataIndex={selectedTestDataIndex}
						highlightVariables={highlightVariables}
						setIsTextEditorFocused={setIsTextEditorFocused}
					/>
				</div>
			);
		}
	}

	return (
		<div
			role="application"
			ref={elementRef}
			className={`absolute ${cursorStyle} ${
				// Add block-specific outline if it's a block and borders are shown (not in export)
				element.type === "block" && !isExportMode && showElementBorders
					? "outline outline-1 outline-dashed outline-gray-500"
					: ""
			}`}
			style={{
				...positionStyles,
				zIndex: effectiveZIndex,
				color: element.color || "inherit",
				boxSizing: "border-box",
				userSelect: "none",
				border:
					element.isAddressField && !isSelected && !isExportMode
						? "1px dashed #ccc"
						: "none",
				pointerEvents: finalPointerEvents,
				opacity: elementOpacity, // Apply opacity
			}}
			onMouseDown={canMouseDown ? handleMouseDown : undefined}
			tabIndex={canKeyDown ? 0 : undefined}
			onKeyDown={canKeyDown ? handleKeyDown : undefined}
			data-element-id={element.id}
			data-element-data={JSON.stringify({
				id: element.id,
				currentPageId:
					typeof element.currentPageId === "string"
						? element.currentPageId
						: "",
				parentId: element.parentId || null,
				x: element.x,
				y: element.y,
				width: element.width,
				height: element.height,
			})}
		>
			{/* Content */}
			{renderElementContent()}

			{/* Controls (Border and Handles) */}
			{renderControlElements()}
		</div>
	);
}
