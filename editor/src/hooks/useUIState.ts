import { useRef, useState } from "react";
import type { Editor } from "../components/RichTextEditor/RichTextEditor";
import type { Element } from "../types/element";

export interface ContextMenuState {
	visible: boolean;
	screenX: number;
	screenY: number;
	pasteX?: number;
	pasteY?: number;
	pastePageId?: string;
}

export function useUIState() {
	const [currentPage, setCurrentPage] = useState<string | null>(null);
	const [clipboard, setClipboard] = useState<
		| Element
		| { block: Element; children: Element[] }
		| { childElement: Element; originalParentId: string }
		| null
	>(null);
	const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
	const [activePageId, setActivePageId] = useState<string | null>(null);
	const [selectedElementId, setSelectedElementId] = useState<string | null>(
		null,
	);
	const [isEditing, setIsEditing] = useState(false);
	const [zoomLevel, setZoomLevel] = useState<number>(1);
	const [activeEditor, setActiveEditor] = useState<Editor | null>(null);
	const [isTextEditorFocused, setIsTextEditorFocused] = useState(false);
	const [deletedPageNumberIds, setDeletedPageNumberIds] = useState<Set<string>>(
		new Set(),
	);
	const [editingBlockId, setEditingBlockId] = useState<string | null>(null);
	const [contextMenu, setContextMenu] = useState<ContextMenuState | null>(null);
	const [highlightVariables, setHighlightVariables] = useState<boolean>(false);
	const [showElementBorders, setShowElementBorders] = useState<boolean>(true);
	const [showFoldMarks, setShowFoldMarks] = useState<boolean>(false);
	const [showPageNumbers, setShowPageNumbers] = useState<boolean>(false);
	const [selectedTestDataIndex, setSelectedTestDataIndex] =
		useState<number>(-1);

	// Refs
	const pageRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
	const scrollContainerRef = useRef<HTMLDivElement | null>(null);
	const originalOverflowRef = useRef<{ x: string; y: string } | null>(null);
	const contextMenuRef = useRef<HTMLDivElement | null>(null);

	return {
		// State values
		currentPage,
		clipboard,
		mousePosition,
		activePageId,
		selectedElementId,
		isEditing,
		zoomLevel,
		activeEditor,
		isTextEditorFocused,
		deletedPageNumberIds,
		editingBlockId,
		contextMenu,
		highlightVariables,
		showElementBorders,
		showFoldMarks,
		showPageNumbers,
		selectedTestDataIndex,

		// Refs
		pageRefs,
		scrollContainerRef,
		originalOverflowRef,
		contextMenuRef,

		// Setters
		setCurrentPage,
		setClipboard,
		setMousePosition,
		setActivePageId,
		setSelectedElementId,
		setIsEditing,
		setZoomLevel,
		setActiveEditor,
		setIsTextEditorFocused,
		setDeletedPageNumberIds,
		setEditingBlockId,
		setContextMenu,
		setHighlightVariables,
		setShowElementBorders,
		setShowFoldMarks,
		setShowPageNumbers,
		setSelectedTestDataIndex,
	};
}
