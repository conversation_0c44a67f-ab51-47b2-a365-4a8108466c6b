import { useCallback, useEffect } from "react";
import { pxToMm, roundToTwoDecimals } from "@/utils/unitConversion";
import type { Editor } from "../components/RichTextEditor/RichTextEditor";
import type { Element } from "../types/element";
import type { PageSettings } from "../types/page";

interface ContextMenuState {
	visible: boolean;
	screenX: number;
	screenY: number;
	pasteX?: number;
	pasteY?: number;
	pastePageId?: string;
}

interface EventHandlersProps {
	// State
	currentPage: string | null;
	clipboard:
		| Element
		| { block: Element; children: Element[] }
		| { childElement: Element; originalParentId: string }
		| null;
	isEditorLocked: boolean;
	activePageId: string | null;
	mousePosition: { x: number; y: number };
	isEditing: boolean;
	isTextEditorFocused: boolean;
	activeEditor: Editor | null;
	elements: Element[];
	editingBlockId: string | null;

	// Setters
	setMousePosition: (pos: { x: number; y: number }) => void;
	setActivePageId: (id: string | null) => void;
	setSelectedElementId: (id: string | null) => void;
	setContextMenu: (menu: ContextMenuState | null) => void;
	setEditingBlockId: (id: string | null) => void;

	// Actions
	handlePasteAction: (
		targetPageId: string,
		pasteX: number,
		pasteY: number,
	) => void;
	undo: () => void;
	redo: () => void;
}

export function useEventHandlers({
	currentPage,
	clipboard,
	isEditorLocked,
	activePageId,
	mousePosition,
	isEditing,
	isTextEditorFocused,
	activeEditor,
	elements,
	editingBlockId,
	setMousePosition,
	setActivePageId,
	setSelectedElementId,
	setContextMenu,
	setEditingBlockId,
	handlePasteAction,
	undo,
	redo,
}: EventHandlersProps) {
	// Keyboard paste handler
	const handlePaste = useCallback(
		(e: KeyboardEvent) => {
			if (
				!currentPage ||
				!clipboard ||
				!(e.ctrlKey || e.metaKey) ||
				e.key !== "v"
			)
				return;
			if (isEditorLocked) return;
			if (activePageId !== currentPage) return;

			// Use mousePosition for paste location
			handlePasteAction(currentPage, mousePosition.x, mousePosition.y);
		},
		[
			currentPage,
			clipboard,
			isEditorLocked,
			activePageId,
			mousePosition,
			handlePasteAction,
		],
	);

	// Keyboard shortcut handler
	const handleKeyDown = useCallback(
		(e: KeyboardEvent) => {
			// Skip if a text editor is focused - let TipTap handle it
			if (isTextEditorFocused && activeEditor) {
				return;
			}

			if (e.ctrlKey || e.metaKey) {
				if (e.key === "z") {
					e.preventDefault();
					if (e.shiftKey) {
						redo();
					} else {
						undo();
					}
				} else if (e.key === "y") {
					e.preventDefault();
					redo();
				}
			}
		},
		[undo, redo, isTextEditorFocused, activeEditor],
	);

	// Element unselect handler
	const handleElementUnselect = useCallback(() => {
		setSelectedElementId(null);
	}, [setSelectedElementId]);

	// Page click handler
	const handlePageClick = useCallback(
		(e: React.MouseEvent<HTMLDivElement>) => {
			if (e.target === e.currentTarget) {
				setSelectedElementId(null);
			}
		},
		[setSelectedElementId],
	);

	// Page context menu handler
	const handlePageContextMenu = useCallback(
		(e: React.MouseEvent<HTMLDivElement>, pageDetails: PageSettings) => {
			// Don't show context menu if text editing is active
			if (isEditing) return;

			// Don't prevent default immediately - we'll decide based on context
			e.stopPropagation();

			const pageElement = e.currentTarget; // This is the div the event is attached to
			const rect = pageElement.getBoundingClientRect();

			const clientX = e.clientX;
			const clientY = e.clientY;

			// Click position relative to the pageElement (outer div) in scaled pixels
			const xPxOuterScaled = clientX - rect.left;
			const yPxOuterScaled = clientY - rect.top;

			// Convert to unscaled pixels (assuming zoomLevel is available in context)
			// Note: This would need zoomLevel to be passed as a prop
			const zoomLevel = 1; // Default fallback
			const xPxOuterUnscaled = xPxOuterScaled / zoomLevel;
			const yPxOuterUnscaled = yPxOuterScaled / zoomLevel;

			// Convert outer unscaled pixel position to mm
			const xMmOuter = pxToMm(xPxOuterUnscaled);
			const yMmOuter = pxToMm(yPxOuterUnscaled);

			// Subtract trim (in mm) to get position relative to the inner content area
			const trimLeftMm = (pageDetails.trim_left ?? 0) * 10;
			const trimTopMm = (pageDetails.trim_top ?? 0) * 10;

			const calculatedPasteX = roundToTwoDecimals(xMmOuter - trimLeftMm);
			const calculatedPasteY = roundToTwoDecimals(yMmOuter - trimTopMm);

			// Check if there's any element at this position
			const elementsOnPage = elements.filter(
				(el) => el.currentPageId === pageDetails.id,
			);
			const clickedOnElement = elementsOnPage.some((element) => {
				// Calculate element bounds
				const elementLeft = element.x;
				const elementTop = element.y;
				const elementRight = element.x + element.width;
				const elementBottom = element.y + element.height;

				// Check if click position is inside an element
				return (
					calculatedPasteX >= elementLeft &&
					calculatedPasteX <= elementRight &&
					calculatedPasteY >= elementTop &&
					calculatedPasteY <= elementBottom
				);
			});

			// Only show our custom context menu if clicked on empty space
			if (!clickedOnElement) {
				// Prevent default for empty space to show our custom menu
				e.preventDefault();
				setContextMenu({
					visible: true,
					screenX: clientX,
					screenY: clientY,
					pasteX: calculatedPasteX,
					pasteY: calculatedPasteY,
					pastePageId: pageDetails.id,
				});
			}
			// If clicked on an element, don't prevent default, let browser's context menu show
		},
		[isEditing, elements, setContextMenu],
	);

	// Page mouse move handler
	const handlePageMouseMove = useCallback(
		(e: React.MouseEvent<HTMLDivElement>, pageDetails: PageSettings) => {
			const pageElement = e.currentTarget;
			const rect = pageElement.getBoundingClientRect();

			// Get position relative to the outer container in pixels
			const xPxOuter = e.clientX - rect.left + pageElement.scrollLeft;
			const yPxOuter = e.clientY - rect.top + pageElement.scrollTop;

			// Convert outer pixel position to mm
			const xMmOuter = pxToMm(xPxOuter);
			const yMmOuter = pxToMm(yPxOuter);

			// Subtract trim (in mm) to get position relative to the inner content area
			const trimLeftCm = pageDetails.trim_left ?? 0;
			const trimTopCm = pageDetails.trim_top ?? 0;
			const xMmInner = xMmOuter - trimLeftCm * 10;
			const yMmInner = yMmOuter - trimTopCm * 10;

			setMousePosition({
				x: roundToTwoDecimals(xMmInner),
				y: roundToTwoDecimals(yMmInner),
			});
			setActivePageId(pageDetails.id);
		},
		[setMousePosition, setActivePageId],
	);

	// Page mouse leave handler
	const handlePageMouseLeave = useCallback(() => {
		setActivePageId(null);
	}, [setActivePageId]);

	// Context menu paste handler
	const handleContextMenuPaste = useCallback(
		(contextMenu: ContextMenuState | null) => {
			if (
				!contextMenu ||
				clipboard === null ||
				contextMenu.pasteX === undefined ||
				contextMenu.pasteY === undefined ||
				!contextMenu.pastePageId
			) {
				setContextMenu(null);
				return;
			}
			handlePasteAction(
				contextMenu.pastePageId,
				contextMenu.pasteX,
				contextMenu.pasteY,
			);
			setContextMenu(null);
		},
		[clipboard, handlePasteAction, setContextMenu],
	);

	// Click outside block edit mode handler
	const handleClickOutsideBlock = useCallback(
		(event: MouseEvent) => {
			if (editingBlockId) {
				const target = event.target as HTMLElement;

				// Check if the click is outside the editing block
				const editingBlockElement = document.querySelector(
					`[data-element-id="${editingBlockId}"]`,
				);
				if (editingBlockElement && !editingBlockElement.contains(target)) {
					// Check for protected UI areas that should not exit container edit mode
					const sidebar = document.querySelector(".sidebar-container");
					const isRadixPortal =
						target.closest("[data-radix-popper-content-wrapper]") ||
						target.closest("[data-radix-portal]") ||
						target.closest('[role="dialog"]') ||
						target.closest('[role="listbox"]');

					// Check if click is in ImageBrowser
					const isImageBrowser = target.closest('[data-image-browser="true"]');

					if (
						(!sidebar || !sidebar.contains(target)) &&
						!isRadixPortal &&
						!isImageBrowser
					) {
						setEditingBlockId(null);
					}
				}
			}
		},
		[editingBlockId, setEditingBlockId],
	);

	// Set up event listeners
	useEffect(() => {
		document.addEventListener("keydown", handlePaste);
		return () => document.removeEventListener("keydown", handlePaste);
	}, [handlePaste]);

	useEffect(() => {
		window.addEventListener("keydown", handleKeyDown);
		return () => window.removeEventListener("keydown", handleKeyDown);
	}, [handleKeyDown]);

	useEffect(() => {
		document.addEventListener("element:unselect", handleElementUnselect);
		return () =>
			document.removeEventListener("element:unselect", handleElementUnselect);
	}, [handleElementUnselect]);

	useEffect(() => {
		document.addEventListener("mousedown", handleClickOutsideBlock);
		return () => {
			document.removeEventListener("mousedown", handleClickOutsideBlock);
		};
	}, [handleClickOutsideBlock]);

	return {
		handlePageClick,
		handlePageContextMenu,
		handlePageMouseMove,
		handlePageMouseLeave,
		handleContextMenuPaste,
	};
}
