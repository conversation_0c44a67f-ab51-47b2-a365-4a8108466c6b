import { act, renderHook } from "@testing-library/react";
import { vi } from "vitest";
import { useHistory } from "../useHistory";

// Mock Date.now to control throttling behavior
const mockDateNow = vi.spyOn(Date, "now");

describe("useHistory", () => {
	beforeEach(() => {
		// Reset the mock date implementation
		mockDateNow.mockReset();
		// Start with a fixed timestamp
		mockDateNow.mockReturnValue(1000);
	});

	// Test initialization
	it("should initialize with the provided initial state", () => {
		const initialState = { count: 0 };
		const { result } = renderHook(() => useHistory(initialState));

		expect(result.current.state).toEqual(initialState);
		expect(result.current.canUndo).toBe(false);
		expect(result.current.canRedo).toBe(false);
	});

	// Test state updates
	describe("setState", () => {
		it("should update state but not add to history on first real update", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// First call should replace initial state, not add to history
			mockDateNow.mockReturnValue(3000); // More than 2s from initial 1000

			act(() => {
				result.current.setState({ count: 1 });
			});

			expect(result.current.state).toEqual({ count: 1 });
			expect(result.current.canUndo).toBe(false); // No history yet - first update replaces initial
			expect(result.current.canRedo).toBe(false);
		});

		it("should add to history on second real update", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// First update replaces initial state
			mockDateNow.mockReturnValue(3000);
			act(() => {
				result.current.setState({ count: 1 });
			});

			expect(result.current.canUndo).toBe(false);

			// Second update should add to history
			mockDateNow.mockReturnValue(6000); // More than 500ms later
			act(() => {
				result.current.setState({ count: 2 });
			});

			expect(result.current.state).toEqual({ count: 2 });
			expect(result.current.canUndo).toBe(true); // Now we can undo
			expect(result.current.canRedo).toBe(false);

			// Undo should go back to first real state, not initial state
			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 1 });
			expect(result.current.canUndo).toBe(false); // Can't undo to initial state
		});

		it("should update state without adding to history when addToHistory is false", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			act(() => {
				result.current.setState({ count: 1 }, false);
			});

			expect(result.current.state).toEqual({ count: 1 });
			expect(result.current.canUndo).toBe(false); // No history entry added
			expect(result.current.canRedo).toBe(false);
		});

		it("should throttle history updates within the throttle duration", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// First update - should replace initial state
			mockDateNow.mockReturnValue(3500);
			act(() => {
				result.current.setState({ count: 1 });
			});

			// Second update within throttle duration - should not add to history but should update present
			mockDateNow.mockReturnValue(3800); // 300ms later (within 500ms throttle)
			act(() => {
				result.current.setState({ count: 2 });
			});

			expect(result.current.state).toEqual({ count: 2 });
			expect(result.current.canUndo).toBe(false); // Still no history since first update replaced initial

			// Third update after throttle duration - should add to history
			mockDateNow.mockReturnValue(4500); // More than 500ms from last history update (4500-3500 = 1000ms)
			act(() => {
				result.current.setState({ count: 3 });
			});

			expect(result.current.canUndo).toBe(true); // Now we have history

			// Verify we can only undo once - back to the state when throttle period ended
			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 2 });
			expect(result.current.canUndo).toBe(false); // Can't undo further
		});

		it("should not add duplicate states to history", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// Add first state change (replaces initial)
			mockDateNow.mockReturnValue(1500);
			act(() => {
				result.current.setState({ count: 1 });
			});

			expect(result.current.canUndo).toBe(false); // First update replaces initial

			// Add second state change (adds to history)
			mockDateNow.mockReturnValue(3000); // More than 500ms later
			act(() => {
				result.current.setState({ count: 2 });
			});

			expect(result.current.canUndo).toBe(true);

			// Try to add the same state again after throttle period
			mockDateNow.mockReturnValue(4000); // More than 500ms later
			act(() => {
				result.current.setState({ count: 2 }); // Same state
			});

			// Should still only be able to undo once (duplicate not added)
			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 1 });
			expect(result.current.canUndo).toBe(false);
		});

		it("should handle deep equality for nested objects", () => {
			const initialState = {
				user: { name: "John", age: 30 },
				items: [1, 2, 3],
			};
			const { result } = renderHook(() => useHistory(initialState));

			// Add a different state (replaces initial)
			mockDateNow.mockReturnValue(1500);
			act(() => {
				result.current.setState({
					user: { name: "Jane", age: 25 },
					items: [4, 5, 6],
				});
			});

			expect(result.current.canUndo).toBe(false); // First update replaces initial

			// Add another state to create history
			mockDateNow.mockReturnValue(3000);
			act(() => {
				result.current.setState({
					user: { name: "Bob", age: 35 },
					items: [7, 8, 9],
				});
			});

			expect(result.current.canUndo).toBe(true);

			// Try to set the same nested state again
			mockDateNow.mockReturnValue(4000);
			act(() => {
				result.current.setState({
					user: { name: "Bob", age: 35 },
					items: [7, 8, 9],
				}); // Same nested state
			});

			// Should still only be able to undo once
			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({
				user: { name: "Jane", age: 25 },
				items: [4, 5, 6],
			});
			expect(result.current.canUndo).toBe(false);
		});

		it("should add state to history when different after throttle period", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// First change (replaces initial)
			mockDateNow.mockReturnValue(1500);
			act(() => {
				result.current.setState({ count: 1 });
			});

			expect(result.current.canUndo).toBe(false);

			// Second different change after throttle period (adds to history)
			mockDateNow.mockReturnValue(3000);
			act(() => {
				result.current.setState({ count: 2 });
			});

			expect(result.current.canUndo).toBe(true);

			// Third different change (adds to history)
			mockDateNow.mockReturnValue(5000);
			act(() => {
				result.current.setState({ count: 3 });
			});

			// Should be able to undo twice now
			expect(result.current.canUndo).toBe(true);

			act(() => {
				result.current.undo(); // Back to { count: 2 }
			});
			expect(result.current.state).toEqual({ count: 2 });
			expect(result.current.canUndo).toBe(true);

			act(() => {
				result.current.undo(); // Back to { count: 1 }
			});
			expect(result.current.state).toEqual({ count: 1 });
			expect(result.current.canUndo).toBe(false); // Can't undo to initial
		});

		it("should handle element movement scenario with draft updates", () => {
			// This simulates how element movement works: draft updates during drag, then final commit
			const initialState = { x: 0, y: 0, content: "element" };
			const { result } = renderHook(() => useHistory(initialState));

			// Initial state is set
			expect(result.current.state).toEqual({ x: 0, y: 0, content: "element" });

			// User starts dragging - this should replace initial state (first real update)
			mockDateNow.mockReturnValue(1000);
			act(() => {
				result.current.setState({ x: 10, y: 0, content: "element" }, true); // Final commit
			});

			expect(result.current.canUndo).toBe(false); // First update replaces initial

			// Now simulate dragging with multiple draft updates (these should not add to history)
			mockDateNow.mockReturnValue(1100);
			act(() => {
				result.current.setState({ x: 15, y: 5, content: "element" }, false); // Draft update
			});

			act(() => {
				result.current.setState({ x: 20, y: 10, content: "element" }, false); // Draft update
			});

			act(() => {
				result.current.setState({ x: 25, y: 15, content: "element" }, false); // Draft update
			});

			// State should be updated to latest draft
			expect(result.current.state).toEqual({
				x: 25,
				y: 15,
				content: "element",
			});

			// Now user releases drag - final commit after throttle period (this should add to history)
			mockDateNow.mockReturnValue(2000); // More than 500ms later
			act(() => {
				result.current.setState({ x: 25, y: 15, content: "element" }, true); // Final commit
			});

			// Should be able to undo to the previous position (x: 10, y: 0)
			expect(result.current.canUndo).toBe(true);
			act(() => {
				result.current.undo();
			});
			expect(result.current.state).toEqual({ x: 10, y: 0, content: "element" });

			// Should NOT be able to undo to initial state
			expect(result.current.canUndo).toBe(false);
		});
	});

	// Test undo functionality
	describe("undo", () => {
		it("should restore previous state", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// Add some history entries (first replaces initial, second adds to history)
			mockDateNow.mockReturnValue(3000);
			act(() => {
				result.current.setState({ count: 1 });
			});

			mockDateNow.mockReturnValue(6000); // After throttle duration
			act(() => {
				result.current.setState({ count: 2 });
			});

			mockDateNow.mockReturnValue(9000); // After throttle duration
			act(() => {
				result.current.setState({ count: 3 });
			});

			// Undo to previous state
			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 2 });
			expect(result.current.canUndo).toBe(true);
			expect(result.current.canRedo).toBe(true);
		});

		it("should do nothing if there is no history to undo", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 0 });
			expect(result.current.canUndo).toBe(false);
			expect(result.current.canRedo).toBe(false);
		});

		it("should handle multiple undo operations", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// Add multiple history entries with sufficient time between them
			// First replaces initial, subsequent ones add to history
			mockDateNow.mockReturnValue(3000);
			act(() => {
				result.current.setState({ count: 1 });
			});

			mockDateNow.mockReturnValue(6000);
			act(() => {
				result.current.setState({ count: 2 });
			});

			mockDateNow.mockReturnValue(9000);
			act(() => {
				result.current.setState({ count: 3 });
			});

			mockDateNow.mockReturnValue(12000);
			act(() => {
				result.current.setState({ count: 4 });
			});

			// Undo multiple times
			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 3 });

			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 2 });

			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 1 }); // Can't undo to initial state
			expect(result.current.canUndo).toBe(false);
			expect(result.current.canRedo).toBe(true);
		});
	});

	// Test redo functionality
	describe("redo", () => {
		it("should restore undone state", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// Add history and then undo
			mockDateNow.mockReturnValue(3000);
			act(() => {
				result.current.setState({ count: 1 });
			});

			mockDateNow.mockReturnValue(6000);
			act(() => {
				result.current.setState({ count: 2 });
			});

			act(() => {
				result.current.undo();
			});

			// Redo to restore the undone state
			act(() => {
				result.current.redo();
			});

			expect(result.current.state).toEqual({ count: 2 });
			expect(result.current.canUndo).toBe(true);
			expect(result.current.canRedo).toBe(false);
		});

		it("should do nothing if there is no state to redo", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			act(() => {
				result.current.redo();
			});

			expect(result.current.state).toEqual({ count: 0 });
			expect(result.current.canUndo).toBe(false);
			expect(result.current.canRedo).toBe(false);
		});

		it("should handle multiple redo operations", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// Add multiple history entries
			mockDateNow.mockReturnValue(3000);
			act(() => {
				result.current.setState({ count: 1 });
			});

			mockDateNow.mockReturnValue(6000);
			act(() => {
				result.current.setState({ count: 2 });
			});

			mockDateNow.mockReturnValue(9000);
			act(() => {
				result.current.setState({ count: 3 });
			});

			mockDateNow.mockReturnValue(12000);
			act(() => {
				result.current.setState({ count: 4 });
			});

			// Undo multiple times
			act(() => {
				result.current.undo();
				result.current.undo();
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 1 }); // Back to first real state

			// Redo multiple times
			act(() => {
				result.current.redo();
			});

			expect(result.current.state).toEqual({ count: 2 });

			act(() => {
				result.current.redo();
			});

			expect(result.current.state).toEqual({ count: 3 });

			act(() => {
				result.current.redo();
			});

			expect(result.current.state).toEqual({ count: 4 });
			expect(result.current.canUndo).toBe(true);
			expect(result.current.canRedo).toBe(false);
		});
	});

	// Test history optimization
	describe("history optimization", () => {
		it("should clear future history when adding new state", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// Add history
			mockDateNow.mockReturnValue(3000);
			act(() => {
				result.current.setState({ count: 1 });
			});

			mockDateNow.mockReturnValue(6000);
			act(() => {
				result.current.setState({ count: 2 });
			});

			mockDateNow.mockReturnValue(9000);
			act(() => {
				result.current.setState({ count: 3 });
			});

			// Undo to create future history
			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 2 });
			expect(result.current.canRedo).toBe(true);

			// Add new state - should clear future history
			mockDateNow.mockReturnValue(12000); // More than 500ms from last update at 9000
			act(() => {
				result.current.setState({ count: 4 });
			});

			expect(result.current.state).toEqual({ count: 4 });
			expect(result.current.canRedo).toBe(false); // Future history should be cleared
		});

		it("should batch operations within throttle duration", () => {
			const initialState = { count: 0 };
			const { result } = renderHook(() => useHistory(initialState));

			// First update - should replace initial state
			mockDateNow.mockReturnValue(3000);
			act(() => {
				result.current.setState({ count: 1 });
			});

			// Multiple updates within throttle duration - should not add to history but should update present
			mockDateNow.mockReturnValue(3200); // 200ms later (within 500ms throttle)
			act(() => {
				result.current.setState({ count: 2 });
			});

			mockDateNow.mockReturnValue(3350); // 350ms from first update (within 500ms throttle)
			act(() => {
				result.current.setState({ count: 3 });
			});

			mockDateNow.mockReturnValue(3450); // 450ms from first update (within 500ms throttle)
			act(() => {
				result.current.setState({ count: 4 });
			});

			expect(result.current.state).toEqual({ count: 4 });
			expect(result.current.canUndo).toBe(false); // Still no history since first update replaced initial

			// Add another update after throttle duration to create actual history
			mockDateNow.mockReturnValue(4000); // More than 500ms later (1000ms from first update)
			act(() => {
				result.current.setState({ count: 5 });
			});

			expect(result.current.canUndo).toBe(true);

			// Undo should go back to the last committed state before throttle
			act(() => {
				result.current.undo();
			});

			expect(result.current.state).toEqual({ count: 4 }); // Last state before new history entry
			expect(result.current.canUndo).toBe(false); // Can't undo further since we don't store initial state
		});
	});
});
