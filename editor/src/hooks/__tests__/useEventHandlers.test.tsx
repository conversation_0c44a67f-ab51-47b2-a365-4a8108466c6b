import { act, renderHook } from "@testing-library/react";
import { vi } from "vitest";
import type { Editor } from "@/components/RichTextEditor/RichTextEditor";
import { pxToMm, roundToTwoDecimals } from "@/utils/unitConversion";
import {
	ElementFactory,
	PageFactory,
} from "../../__tests__/utils/mock-factories";
import { useEventHandlers } from "../useEventHandlers";

describe("useEventHandlers", () => {
	// Setup common test variables
	const mockPage = PageFactory.createPage({
		id: "page-1",
		trim_left: 0.5,
		trim_top: 0.5,
		trim_right: 0.5,
		trim_bottom: 0.5,
		width: 210, // A4 width in mm
		height: 297, // A4 height in mm
	});

	const mockElements = [
		ElementFactory.createTextElement({
			id: "text-1",
			currentPageId: "page-1",
			x: 10,
			y: 10,
			width: 50,
			height: 20,
		}),
	];

	const mockSetMousePosition = vi.fn();
	const mockSetActivePageId = vi.fn();
	const mockSetSelectedElementId = vi.fn();
	const mockSetContextMenu = vi.fn();
	const mockSetEditingBlockId = vi.fn();
	const mockHandlePasteAction = vi.fn();
	const mockUndo = vi.fn();
	const mockRedo = vi.fn();

	const defaultProps = {
		currentPage: "page-1",
		clipboard: null,
		isEditorLocked: false,
		activePageId: "page-1",
		mousePosition: { x: 50, y: 50 },
		isEditing: false,
		isTextEditorFocused: false,
		activeEditor: {} as unknown as Editor,
		elements: mockElements,
		editingBlockId: null,
		setMousePosition: mockSetMousePosition,
		setActivePageId: mockSetActivePageId,
		setSelectedElementId: mockSetSelectedElementId,
		setContextMenu: mockSetContextMenu,
		setEditingBlockId: mockSetEditingBlockId,
		handlePasteAction: mockHandlePasteAction,
		undo: mockUndo,
		redo: mockRedo,
	};

	beforeEach(() => {
		vi.clearAllMocks();

		// Setup document body for DOM testing
		document.body.innerHTML = `
      <div id="editor">
        <div id="page-1" data-page-id="page-1"></div>
        <div id="block-1" data-element-id="block-1"></div>
        <div class="sidebar-container"></div>
      </div>
    `;
	});

	// Test keyboard shortcuts
	describe("keyboard shortcuts", () => {
		it("should handle Ctrl+Z for undo", () => {
			renderHook(() => useEventHandlers(defaultProps));

			// Simulate Ctrl+Z keydown
			act(() => {
				const event = new KeyboardEvent("keydown", {
					key: "z",
					ctrlKey: true,
					bubbles: true,
				});
				document.dispatchEvent(event);
			});

			expect(mockUndo).toHaveBeenCalled();
		});

		it("should handle Cmd+Z for undo on Mac", () => {
			renderHook(() => useEventHandlers(defaultProps));

			// Simulate Cmd+Z keydown
			act(() => {
				const event = new KeyboardEvent("keydown", {
					key: "z",
					metaKey: true,
					bubbles: true,
				});
				document.dispatchEvent(event);
			});

			expect(mockUndo).toHaveBeenCalled();
		});

		it("should handle Ctrl+Shift+Z for redo", () => {
			renderHook(() => useEventHandlers(defaultProps));

			// Simulate Ctrl+Shift+Z keydown
			act(() => {
				const event = new KeyboardEvent("keydown", {
					key: "z",
					ctrlKey: true,
					shiftKey: true,
					bubbles: true,
				});
				document.dispatchEvent(event);
			});

			expect(mockRedo).toHaveBeenCalled();
		});

		it("should handle Ctrl+Y for redo", () => {
			renderHook(() => useEventHandlers(defaultProps));

			// Simulate Ctrl+Y keydown
			act(() => {
				const event = new KeyboardEvent("keydown", {
					key: "y",
					ctrlKey: true,
					bubbles: true,
				});
				document.dispatchEvent(event);
			});

			expect(mockRedo).toHaveBeenCalled();
		});

		it("should not handle keyboard shortcuts when text editor is focused", () => {
			renderHook(() =>
				useEventHandlers({
					...defaultProps,
					isTextEditorFocused: true,
					activeEditor: {} as unknown as Editor,
				}),
			);

			// Simulate Ctrl+Z keydown
			act(() => {
				const event = new KeyboardEvent("keydown", {
					key: "z",
					ctrlKey: true,
					bubbles: true,
				});
				document.dispatchEvent(event);
			});

			expect(mockUndo).not.toHaveBeenCalled();
		});
	});

	// Test paste functionality
	describe("paste functionality", () => {
		it("should handle keyboard paste (Ctrl+V)", () => {
			const clipboardContent = ElementFactory.createTextElement({
				id: "clipboard-text",
			});

			renderHook(() =>
				useEventHandlers({
					...defaultProps,
					clipboard: clipboardContent,
				}),
			);

			// Simulate Ctrl+V keydown
			act(() => {
				const event = new KeyboardEvent("keydown", {
					key: "v",
					ctrlKey: true,
					bubbles: true,
				});
				document.dispatchEvent(event);
			});

			expect(mockHandlePasteAction).toHaveBeenCalledWith("page-1", 50, 50);
		});

		it("should not paste when editor is locked", () => {
			const clipboardContent = ElementFactory.createTextElement({
				id: "clipboard-text",
			});

			renderHook(() =>
				useEventHandlers({
					...defaultProps,
					clipboard: clipboardContent,
					isEditorLocked: true,
				}),
			);

			// Simulate Ctrl+V keydown
			act(() => {
				const event = new KeyboardEvent("keydown", {
					key: "v",
					ctrlKey: true,
					bubbles: true,
				});
				document.dispatchEvent(event);
			});

			expect(mockHandlePasteAction).not.toHaveBeenCalled();
		});

		it("should handle context menu paste", () => {
			const clipboardContent = ElementFactory.createTextElement({
				id: "clipboard-text",
			});
			const contextMenu = {
				visible: true,
				screenX: 100,
				screenY: 100,
				pasteX: 25,
				pasteY: 30,
				pastePageId: "page-1",
			};

			const { result } = renderHook(() =>
				useEventHandlers({
					...defaultProps,
					clipboard: clipboardContent,
				}),
			);
			act(() => {
				result.current.handleContextMenuPaste(contextMenu);
			});
			expect(mockHandlePasteAction).toHaveBeenCalledWith("page-1", 25, 30);
			expect(mockSetContextMenu).toHaveBeenCalledWith(null);
		});
	});

	// Test page interaction handlers
	describe("page interaction handlers", () => {
		it("should handle page click to unselect elements", () => {
			const { result } = renderHook(() => useEventHandlers(defaultProps));
			const mockEvent = {
				target: document.getElementById("page-1"),
				currentTarget: document.getElementById("page-1"),
			} as unknown as React.MouseEvent<HTMLDivElement>;
			act(() => {
				result.current.handlePageClick(mockEvent);
			});
			expect(mockSetSelectedElementId).toHaveBeenCalledWith(null);
		});

		it("should not unselect elements when clicking on an element", () => {
			const { result } = renderHook(() => useEventHandlers(defaultProps));
			const mockEvent = {
				target: document.createElement("div"),
				currentTarget: document.getElementById("page-1"),
			} as unknown as React.MouseEvent<HTMLDivElement>;
			act(() => {
				result.current.handlePageClick(mockEvent);
			});
			expect(mockSetSelectedElementId).not.toHaveBeenCalled();
		});

		it("should handle page mouse move to update position", () => {
			const { result } = renderHook(() => useEventHandlers(defaultProps));
			const mockRect = { left: 10, top: 20, width: 210, height: 297 };
			const mockPageElement = document.getElementById("page-1");
			if (mockPageElement) {
				mockPageElement.getBoundingClientRect = vi
					.fn()
					.mockReturnValue(mockRect);
				mockPageElement.scrollLeft = 0;
				mockPageElement.scrollTop = 0;
			}
			const mockEvent = {
				clientX: 60,
				clientY: 70,
				currentTarget: mockPageElement,
			} as unknown as React.MouseEvent<HTMLDivElement>;
			act(() => {
				result.current.handlePageMouseMove(mockEvent, mockPage);
			});
			const expectedX = roundToTwoDecimals(pxToMm(50) - 5);
			const expectedY = roundToTwoDecimals(pxToMm(50) - 5);
			expect(mockSetMousePosition).toHaveBeenCalledWith({
				x: expectedX,
				y: expectedY,
			});
			expect(mockSetActivePageId).toHaveBeenCalledWith("page-1");
		});

		it("should handle page mouse leave", () => {
			const { result } = renderHook(() => useEventHandlers(defaultProps));
			act(() => {
				result.current.handlePageMouseLeave();
			});
			expect(mockSetActivePageId).toHaveBeenCalledWith(null);
		});
	});

	// Test context menu handling
	describe("context menu handling", () => {
		it("should show context menu when right-clicking on empty space", () => {
			// Use elements that don't overlap with our click position
			const nonOverlappingElements = [
				ElementFactory.createTextElement({
					id: "text-1",
					currentPageId: "page-1",
					x: 1000, // Far outside any possible click position
					y: 1000,
					width: 50,
					height: 20,
				}),
			];

			const props = {
				currentPage: mockPage.id,
				clipboard: null,
				isEditorLocked: false,
				activePageId: mockPage.id,
				mousePosition: { x: 50, y: 50 },
				isEditing: false,
				isTextEditorFocused: false,
				activeEditor: {} as unknown as Editor,
				elements: nonOverlappingElements,
				editingBlockId: null,
				setMousePosition: mockSetMousePosition,
				setActivePageId: mockSetActivePageId,
				setSelectedElementId: mockSetSelectedElementId,
				setContextMenu: mockSetContextMenu,
				setEditingBlockId: mockSetEditingBlockId,
				handlePasteAction: mockHandlePasteAction,
				undo: mockUndo,
				redo: mockRedo,
			};
			const { result } = renderHook(() => useEventHandlers(props));

			// Mock getBoundingClientRect for the page element
			const mockRect = { left: 10, top: 20, width: 210, height: 297 };
			const mockPageElement = document.getElementById("page-1");
			if (mockPageElement) {
				mockPageElement.getBoundingClientRect = vi
					.fn()
					.mockReturnValue(mockRect);
			}

			// Create a mock context menu event (right-click) at a position with no elements
			const mockEvent = {
				clientX: 110, // 100px from left of page element
				clientY: 120, // 100px from top of page element
				currentTarget: mockPageElement,
				preventDefault: vi.fn(),
				stopPropagation: vi.fn(),
			} as unknown as React.MouseEvent<HTMLDivElement>;

			act(() => {
				result.current.handlePageContextMenu(mockEvent, mockPage);
			});
			// Debug logs
			const viPreventDefault = mockEvent.preventDefault as unknown as {
				mock: { calls: unknown[] };
			};
			const viStopPropagation = mockEvent.stopPropagation as unknown as {
				mock: { calls: unknown[] };
			};
			console.log("preventDefault called:", viPreventDefault.mock.calls.length);
			console.log(
				"stopPropagation called:",
				viStopPropagation.mock.calls.length,
			);
			console.log("setContextMenu calls:", mockSetContextMenu.mock.calls);

			expect(mockEvent.preventDefault).toHaveBeenCalled();
			expect(mockEvent.stopPropagation).toHaveBeenCalled();
			expect(mockSetContextMenu).toHaveBeenCalled();

			// Verify context menu position and paste coordinates
			const contextMenuCall = mockSetContextMenu.mock.calls[0][0];
			expect(contextMenuCall.visible).toBe(true);
			expect(contextMenuCall.screenX).toBe(110);
			expect(contextMenuCall.screenY).toBe(120);
			expect(contextMenuCall.pastePageId).toBe("page-1");
		});

		it("should not show context menu when right-clicking on an element", () => {
			// Create an element that covers the area where we'll right-click
			const elementAtClickPosition = ElementFactory.createTextElement({
				id: "element-at-click",
				currentPageId: "page-1",
				x: 20, // in mm
				y: 20, // in mm
				width: 50, // in mm
				height: 30, // in mm
			});

			renderHook(() =>
				useEventHandlers({
					...defaultProps,
					elements: [elementAtClickPosition],
				}),
			);

			// Mock getBoundingClientRect for the page element
			const mockRect = { left: 10, top: 20, width: 210, height: 297 };
			const mockPageElement = document.getElementById("page-1");
			if (mockPageElement) {
				mockPageElement.getBoundingClientRect = vi
					.fn()
					.mockReturnValue(mockRect);
			}

			// Create a mock context menu event (right-click) at a position with an element
			// The click position will convert to approximately 25mm, 25mm which is within the element bounds
			const mockEvent = {
				clientX: 110, // Will convert to ~25mm after adjustments
				clientY: 120, // Will convert to ~25mm after adjustments
				currentTarget: mockPageElement,
				preventDefault: vi.fn(),
				stopPropagation: vi.fn(),
			} as unknown as React.MouseEvent<HTMLDivElement>;

			const { result } = renderHook(() => useEventHandlers(defaultProps));
			act(() => {
				result.current.handlePageContextMenu(mockEvent, mockPage);
			});

			// Context menu should not be prevented (to allow browser's default menu)
			expect(mockEvent.preventDefault).not.toHaveBeenCalled();
			expect(mockEvent.stopPropagation).toHaveBeenCalled();
			expect(mockSetContextMenu).not.toHaveBeenCalled();
		});

		it("should not show context menu when editing text", () => {
			renderHook(() =>
				useEventHandlers({
					...defaultProps,
					isEditing: true,
				}),
			);

			// Mock getBoundingClientRect for the page element
			const mockRect = { left: 10, top: 20, width: 210, height: 297 };
			const mockPageElement = document.getElementById("page-1");
			if (mockPageElement) {
				mockPageElement.getBoundingClientRect = vi
					.fn()
					.mockReturnValue(mockRect);
			}

			// Create a mock context menu event (right-click)
			const mockEvent = {
				clientX: 110,
				clientY: 120,
				currentTarget: mockPageElement,
				preventDefault: vi.fn(),
				stopPropagation: vi.fn(),
			} as unknown as React.MouseEvent<HTMLDivElement>;

			const { result } = renderHook(() => useEventHandlers(defaultProps));
			act(() => {
				result.current.handlePageContextMenu(mockEvent, mockPage);
			});

			// When editing, the function returns early, so stopPropagation is not called
			expect(mockSetContextMenu).not.toHaveBeenCalled();
		});
	});

	// Test block editing mode
	describe("block editing mode", () => {
		it("should exit block editing mode when clicking outside the block", () => {
			renderHook(() =>
				useEventHandlers({
					...defaultProps,
					editingBlockId: "block-1",
				}),
			);

			// Simulate clicking outside the block
			act(() => {
				const event = new MouseEvent("mousedown", {
					bubbles: true,
					cancelable: true,
				});
				document.getElementById("editor")?.dispatchEvent(event);
			});

			expect(mockSetEditingBlockId).toHaveBeenCalledWith(null);
		});

		it("should not exit block editing mode when clicking inside the block", () => {
			renderHook(() =>
				useEventHandlers({
					...defaultProps,
					editingBlockId: "block-1",
				}),
			);

			// Simulate clicking inside the block
			act(() => {
				const event = new MouseEvent("mousedown", {
					bubbles: true,
					cancelable: true,
				});
				document.getElementById("block-1")?.dispatchEvent(event);
			});

			expect(mockSetEditingBlockId).not.toHaveBeenCalled();
		});

		it("should not exit block editing mode when clicking on sidebar", () => {
			renderHook(() =>
				useEventHandlers({
					...defaultProps,
					editingBlockId: "block-1",
				}),
			);

			// Simulate clicking on sidebar
			act(() => {
				const event = new MouseEvent("mousedown", {
					bubbles: true,
					cancelable: true,
				});
				document.querySelector(".sidebar-container")?.dispatchEvent(event);
			});

			expect(mockSetEditingBlockId).not.toHaveBeenCalled();
		});
	});

	// Test element unselect event
	describe("element unselect event", () => {
		it("should handle element:unselect custom event", () => {
			renderHook(() => useEventHandlers(defaultProps));

			// Simulate custom element:unselect event
			act(() => {
				const event = new CustomEvent("element:unselect");
				document.dispatchEvent(event);
			});

			expect(mockSetSelectedElementId).toHaveBeenCalledWith(null);
		});
	});
});
