import { act, renderHook } from "@testing-library/react";
import { vi } from "vitest";
import type { Editor } from "@/components/RichTextEditor/RichTextEditor";
import { ElementFactory } from "../../__tests__/utils/mock-factories";
import { useUIState } from "../useUIState";

describe("useUIState", () => {
	// Test initialization
	it("should initialize with default state", () => {
		const { result } = renderHook(() => useUIState());

		expect(result.current.currentPage).toBeNull();
		expect(result.current.clipboard).toBeNull();
		expect(result.current.mousePosition).toEqual({ x: 0, y: 0 });
		expect(result.current.activePageId).toBeNull();
		expect(result.current.selectedElementId).toBeNull();
		expect(result.current.isEditing).toBe(false);
		expect(result.current.zoomLevel).toBe(1);
		expect(result.current.activeEditor).toBeNull();
		expect(result.current.isTextEditorFocused).toBe(false);
		expect(result.current.deletedPageNumberIds).toEqual(new Set());
		expect(result.current.editingBlockId).toBeNull();
		expect(result.current.contextMenu).toBeNull();
		expect(result.current.highlightVariables).toBe(false);
		expect(result.current.showElementBorders).toBe(true);
		expect(result.current.showFoldMarks).toBe(false);
		expect(result.current.showPageNumbers).toBe(false);
		expect(result.current.selectedTestDataIndex).toBe(-1);

		// Refs should be initialized
		expect(result.current.pageRefs.current).toEqual({});
		expect(result.current.scrollContainerRef.current).toBeNull();
		expect(result.current.originalOverflowRef.current).toBeNull();
		expect(result.current.contextMenuRef.current).toBeNull();
	});

	// Test state updates
	describe("state updates", () => {
		it("should update currentPage state", () => {
			const { result } = renderHook(() => useUIState());

			act(() => {
				result.current.setCurrentPage("page-1");
			});

			expect(result.current.currentPage).toBe("page-1");
		});

		it("should update clipboard state", () => {
			const { result } = renderHook(() => useUIState());
			const mockElement = ElementFactory.createTextElement();

			act(() => {
				result.current.setClipboard(mockElement);
			});

			expect(result.current.clipboard).toEqual(mockElement);
		});

		it("should update mousePosition state", () => {
			const { result } = renderHook(() => useUIState());
			const newPosition = { x: 100, y: 200 };

			act(() => {
				result.current.setMousePosition(newPosition);
			});

			expect(result.current.mousePosition).toEqual(newPosition);
		});

		it("should update activePageId state", () => {
			const { result } = renderHook(() => useUIState());

			act(() => {
				result.current.setActivePageId("page-1");
			});

			expect(result.current.activePageId).toBe("page-1");
		});

		it("should update selectedElementId state", () => {
			const { result } = renderHook(() => useUIState());

			act(() => {
				result.current.setSelectedElementId("element-1");
			});

			expect(result.current.selectedElementId).toBe("element-1");
		});
	});

	// Test editing state
	describe("editing state", () => {
		it("should update isEditing state", () => {
			const { result } = renderHook(() => useUIState());

			act(() => {
				result.current.setIsEditing(true);
			});

			expect(result.current.isEditing).toBe(true);
		});

		it("should update editingBlockId state", () => {
			const { result } = renderHook(() => useUIState());

			act(() => {
				result.current.setEditingBlockId("block-1");
			});

			expect(result.current.editingBlockId).toBe("block-1");
		});

		it("should update isTextEditorFocused state", () => {
			const { result } = renderHook(() => useUIState());

			act(() => {
				result.current.setIsTextEditorFocused(true);
			});

			expect(result.current.isTextEditorFocused).toBe(true);
		});

		it("should update activeEditor state", () => {
			const { result } = renderHook(() => useUIState());
			const mockEditor = { commands: { focus: vi.fn() } } as unknown as Editor;

			act(() => {
				result.current.setActiveEditor(mockEditor);
			});

			expect(result.current.activeEditor).toBe(mockEditor);
		});
	});

	// Test view settings
	describe("view settings", () => {
		it("should update zoomLevel state", () => {
			const { result } = renderHook(() => useUIState());

			act(() => {
				result.current.setZoomLevel(1.5);
			});

			expect(result.current.zoomLevel).toBe(1.5);
		});

		it("should update showElementBorders state", () => {
			const { result } = renderHook(() => useUIState());

			act(() => {
				result.current.setShowElementBorders(false);
			});

			expect(result.current.showElementBorders).toBe(false);
		});

		it("should update showFoldMarks state", () => {
			const { result } = renderHook(() => useUIState());

			act(() => {
				result.current.setShowFoldMarks(true);
			});

			expect(result.current.showFoldMarks).toBe(true);
		});

		it("should update showPageNumbers state", () => {
			const { result } = renderHook(() => useUIState());

			act(() => {
				result.current.setShowPageNumbers(true);
			});

			expect(result.current.showPageNumbers).toBe(true);
		});

		it("should update highlightVariables state", () => {
			const { result } = renderHook(() => useUIState());

			act(() => {
				result.current.setHighlightVariables(true);
			});

			expect(result.current.highlightVariables).toBe(true);
		});
	});

	// Test context menu state
	describe("context menu state", () => {
		it("should update contextMenu state", () => {
			const { result } = renderHook(() => useUIState());
			const mockContextMenu = {
				visible: true,
				screenX: 100,
				screenY: 200,
				pasteX: 50,
				pasteY: 60,
				pastePageId: "page-1",
			};

			act(() => {
				result.current.setContextMenu(mockContextMenu);
			});

			expect(result.current.contextMenu).toEqual(mockContextMenu);
		});
	});

	// Test page number state
	describe("page number state", () => {
		it("should update deletedPageNumberIds state", () => {
			const { result } = renderHook(() => useUIState());
			const mockDeletedIds = new Set(["page-1", "page-2"]);

			act(() => {
				result.current.setDeletedPageNumberIds(mockDeletedIds);
			});

			expect(result.current.deletedPageNumberIds).toEqual(mockDeletedIds);
		});

		it("should update selectedTestDataIndex state", () => {
			const { result } = renderHook(() => useUIState());

			act(() => {
				result.current.setSelectedTestDataIndex(2);
			});

			expect(result.current.selectedTestDataIndex).toBe(2);
		});
	});

	// Test refs
	describe("refs", () => {
		it("should provide access to refs", () => {
			const { result } = renderHook(() => useUIState());

			expect(result.current.pageRefs).toBeDefined();
			expect(result.current.scrollContainerRef).toBeDefined();
			expect(result.current.originalOverflowRef).toBeDefined();
			expect(result.current.contextMenuRef).toBeDefined();
		});
	});
});
