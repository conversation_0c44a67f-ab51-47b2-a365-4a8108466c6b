import { useCallback, useRef, useState } from "react";

interface HistoryState<T> {
	past: T[];
	present: T;
	future: T[];
}

// Deep equality check function
function deepEqual<T>(a: T, b: T): boolean {
	if (a === b) return true;

	if (a == null || b == null) return a === b;

	if (typeof a !== typeof b) return false;

	if (typeof a !== "object") return a === b;

	if (Array.isArray(a) !== Array.isArray(b)) return false;

	if (Array.isArray(a)) {
		const arrA = a as unknown as Array<unknown>;
		const arrB = b as unknown as Array<unknown>;
		if (arrA.length !== arrB.length) return false;
		return arrA.every((item, index) => deepEqual(item, arrB[index]));
	}

	const objA = a as Record<string, unknown>;
	const objB = b as unknown as Record<string, unknown>;

	const keysA = Object.keys(objA);
	const keysB = Object.keys(objB);

	if (keysA.length !== keysB.length) return false;

	return keysA.every((key) => deepEqual(objA[key], objB[key]));
}

export function useHistory<T>(initialState: T) {
	const [history, setHistory] = useState<HistoryState<T>>({
		past: [],
		present: initialState,
		future: [],
	});

	// Add a timestamp reference to track the last time a state was added to history
	const lastHistoryUpdateRef = useRef<number>(0);
	// Track the state that should be added to past when making the next history commit
	const stateForPastRef = useRef<T>(initialState);
	// Track whether any draft updates have occurred since the last history commit
	const hasDraftsSinceLastCommitRef = useRef<boolean>(false);
	// Track if this is the first real state update (to avoid adding initial page load to history)
	const isFirstRealUpdateRef = useRef<boolean>(true);
	// Throttle duration in milliseconds (0.5 seconds)
	const THROTTLE_DURATION = 500;

	const setState = useCallback(
		(newPresent: T, addToHistory: boolean = true) => {
			setHistory((prev) => {
				// If we shouldn't add to history, just update the present state
				if (!addToHistory) {
					hasDraftsSinceLastCommitRef.current = true;
					return {
						...prev,
						present: newPresent,
					};
				}

				// Check if this is the first real state update (after initial page load)
				// If so, replace the initial state instead of adding to history
				if (isFirstRealUpdateRef.current) {
					isFirstRealUpdateRef.current = false;
					stateForPastRef.current = newPresent;
					hasDraftsSinceLastCommitRef.current = false;
					// Update timestamp so subsequent updates are properly throttled
					lastHistoryUpdateRef.current = Date.now();
					return {
						past: [], // Keep past empty for the first real update
						present: newPresent,
						future: [],
					};
				}

				// Check if the new state is different from the current present state
				// If drafts have occurred since last commit, we should add to history even if state appears same
				const isStateChanged =
					hasDraftsSinceLastCommitRef.current ||
					!deepEqual(prev.present, newPresent);

				// If state hasn't changed and no drafts, just update present without adding to history
				if (!isStateChanged) {
					return {
						...prev,
						present: newPresent,
					};
				}

				// Get current timestamp
				const now = Date.now();

				// Check if we should throttle adding to history
				// Only add to history if more than THROTTLE_DURATION has passed since the last update
				// BUT: always add to history if we have draft updates that need to be committed
				const shouldAddToHistory =
					now - lastHistoryUpdateRef.current > THROTTLE_DURATION ||
					hasDraftsSinceLastCommitRef.current;

				// If we're throttling and not enough time has passed, just update present without adding to history
				if (!shouldAddToHistory) {
					// Don't mark as drafts for throttled updates - they're real commits, just delayed
					return {
						...prev,
						present: newPresent,
					};
				}

				// Update the timestamp reference only when we actually add to history
				lastHistoryUpdateRef.current = now;

				// Check if we had drafts before resetting the flag
				const hadDraftsSinceLastCommit = hasDraftsSinceLastCommitRef.current;
				// Reset the drafts flag since we're committing to history
				hasDraftsSinceLastCommitRef.current = false;

				// Add to history - use prev.present for throttled updates, or stateForPastRef for draft scenarios
				const stateToAddToPast = hadDraftsSinceLastCommit
					? stateForPastRef.current
					: prev.present;
				const newHistory = {
					past: [...prev.past, stateToAddToPast],
					present: newPresent,
					future: [], // Clear future when adding new history entry
				};

				// Update the state that should go to past for the next history commit
				stateForPastRef.current = newPresent;

				return newHistory;
			});
		},
		[],
	);

	const undo = useCallback(() => {
		setHistory((prev) => {
			if (prev.past.length === 0) return prev;

			// Take the last item from past as new present
			const previous = prev.past[prev.past.length - 1];
			const newPast = prev.past.slice(0, -1);

			// Update stateForPastRef to track what should go to past for the next commit
			stateForPastRef.current = previous;
			// Reset drafts flag when undoing
			hasDraftsSinceLastCommitRef.current = false;

			return {
				past: newPast,
				present: previous,
				future: [prev.present, ...prev.future],
			};
		});
	}, []);

	const redo = useCallback(() => {
		setHistory((prev) => {
			if (prev.future.length === 0) return prev;

			const next = prev.future[0];
			const newFuture = prev.future.slice(1);

			// Update stateForPastRef to track what should go to past for the next commit
			stateForPastRef.current = next;
			// Reset drafts flag when redoing
			hasDraftsSinceLastCommitRef.current = false;

			return {
				past: [...prev.past, prev.present],
				present: next,
				future: newFuture,
			};
		});
	}, []);

	return {
		state: history.present,
		setState,
		undo,
		redo,
		canUndo: history.past.length > 0,
		canRedo: history.future.length > 0,
	};
}
