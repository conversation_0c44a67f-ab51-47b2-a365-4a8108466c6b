import type { Element, ElementType, ShapeType } from "@/types/element";
import type { PageSettings } from "@/types/page";
import type { EditorState } from "./test-utils";

// Utility function to generate unique IDs
function generateId(prefix: string = "test"): string {
	return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
}

// Element Factory
export const ElementFactory = {
	/**
	 * Create a basic text element with realistic data
	 */
	createTextElement(overrides: Partial<Element> = {}): Element {
		return {
			id: generateId("text"),
			currentPageId: "page-1",
			type: "text",
			x: 50,
			y: 100,
			width: 200,
			height: 50,
			rotation: 0,
			zIndex: 1,
			content: "Sample text content",
			color: "#000000",
			backgroundColor: "transparent",
			verticalAlign: "top",
			isLocked: false,
			...overrides,
		};
	},

	/**
	 * Create a shape element with realistic data
	 */
	createShapeElement(overrides: Partial<Element> = {}): Element {
		return {
			id: generateId("shape"),
			currentPageId: "page-1",
			type: "shape",
			x: 100,
			y: 150,
			width: 100,
			height: 100,
			rotation: 0,
			zIndex: 2,
			shapeType: "rectangle" as ShapeType,
			color: "#333333",
			backgroundColor: "#f0f0f0",
			borderRadius: [0, 0, 0, 0],
			borderWidth: 0,
			borderColor: "#000000",
			isLocked: false,
			...overrides,
		};
	},

	/**
	 * Create an image element with realistic data
	 */
	createImageElement(overrides: Partial<Element> = {}): Element {
		return {
			id: generateId("image"),
			currentPageId: "page-1",
			type: "image",
			x: 200,
			y: 200,
			width: 150,
			height: 100,
			rotation: 0,
			zIndex: 3,
			content: "/api/image/sample-image-hash",
			aspectRatio: 1.5,
			isLocked: false,
			...overrides,
		};
	},

	/**
	 * Create a table element with realistic data
	 */
	createTableElement(overrides: Partial<Element> = {}): Element {
		return {
			id: generateId("table"),
			currentPageId: "page-1",
			type: "table",
			x: 50,
			y: 300,
			width: 300,
			height: 150,
			rotation: 0,
			zIndex: 4,
			tableProperties: {
				rows: 3,
				columns: 2,
				borderStyle: "solid",
				cells: [
					[
						{
							content: "A1",
							backgroundColor: "#fff",
							colspan: 1,
							rowspan: 1,
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
						{
							content: "A2",
							backgroundColor: "#fff",
							colspan: 1,
							rowspan: 1,
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
					],
					[
						{
							content: "B1",
							backgroundColor: "#fff",
							colspan: 1,
							rowspan: 1,
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
						{
							content: "B2",
							backgroundColor: "#fff",
							colspan: 1,
							rowspan: 1,
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
					],
					[
						{
							content: "C1",
							backgroundColor: "#fff",
							colspan: 1,
							rowspan: 1,
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
						{
							content: "C2",
							backgroundColor: "#fff",
							colspan: 1,
							rowspan: 1,
							borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
						},
					],
				],
				borderWidth: 1,
				borderColor: "#cccccc",
			},
			isLocked: false,
			...overrides,
		};
	},

	/**
	 * Create a block element with child elements
	 */
	createBlockElement(overrides: Partial<Element> = {}): Element {
		const blockId = generateId("block");
		const childIds = [generateId("child"), generateId("child")];

		return {
			id: blockId,
			currentPageId: "page-1",
			type: "block",
			x: 50,
			y: 400,
			width: 250,
			height: 100,
			rotation: 0,
			zIndex: 5,
			childElementIds: childIds,
			backgroundColor: "#f9f9f9",
			isLocked: false,
			...overrides,
		};
	},

	/**
	 * Create an address element with realistic data
	 */
	createAddressElement(overrides: Partial<Element> = {}): Element {
		return {
			id: generateId("address"),
			currentPageId: "page-1",
			type: "address",
			x: 400,
			y: 100,
			width: 200,
			height: 120,
			rotation: 0,
			zIndex: 6,
			content: "{{recipient_name}}\n{{recipient_address}}",
			isAddressField: true,
			addressLayout: {
				sender_y: 20,
				recipient_y: 60,
				blocker_y: 40,
			},
			isLocked: false,
			...overrides,
		};
	},

	/**
	 * Create multiple elements on a specific page
	 */
	createElementsOnPage(pageId: string, count: number): Element[] {
		const elements: Element[] = [];
		const elementTypes: ElementType[] = ["text", "shape", "image"];

		for (let i = 0; i < count; i++) {
			const type = elementTypes[i % elementTypes.length];
			const baseY = 50 + i * 80;

			switch (type) {
				case "text":
					elements.push(
						ElementFactory.createTextElement({
							currentPageId: pageId,
							y: baseY,
							zIndex: i + 1,
						}),
					);
					break;
				case "shape":
					elements.push(
						ElementFactory.createShapeElement({
							currentPageId: pageId,
							y: baseY,
							zIndex: i + 1,
						}),
					);
					break;
				case "image":
					elements.push(
						ElementFactory.createImageElement({
							currentPageId: pageId,
							y: baseY,
							zIndex: i + 1,
						}),
					);
					break;
			}
		}

		return elements;
	},

	/**
	 * Create a complex element with nested properties
	 */
	createComplexElement(type: ElementType = "text"): Element {
		const baseElement = ElementFactory.createTextElement();

		return {
			...baseElement,
			type,
			rotation: 15,
			borderRadius: [5, 10, 5, 10],
			isAbsolutePosition: true,
			verticalAlign: "middle",
			content: "Complex element with {{variable}} content",
		};
	},
};

// Page Factory
export const PageFactory = {
	/**
	 * Create a standard portrait page
	 */
	createPage(overrides: Partial<PageSettings> = {}): PageSettings {
		return {
			id: generateId("page"),
			format: "A4",
			orientation: "portrait",
			width: 210, // A4 width in mm
			height: 297, // A4 height in mm
			trim_top: 5,
			trim_right: 5,
			trim_bottom: 5,
			trim_left: 5,
			pagenumber_x: 105,
			pagenumber_y: 280,
			address_x: 20,
			address_y: 50,
			...overrides,
		};
	},

	/**
	 * Create a landscape page
	 */
	createLandscapePage(overrides: Partial<PageSettings> = {}): PageSettings {
		return PageFactory.createPage({
			orientation: "landscape",
			width: 297, // A4 landscape width
			height: 210, // A4 landscape height
			pagenumber_x: 148,
			pagenumber_y: 190,
			...overrides,
		});
	},

	/**
	 * Create a custom sized page
	 */
	createCustomPage(
		width: number,
		height: number,
		overrides: Partial<PageSettings> = {},
	): PageSettings {
		return PageFactory.createPage({
			format: "Custom",
			width,
			height,
			pagenumber_x: width / 2,
			pagenumber_y: height - 20,
			...overrides,
		});
	},

	/**
	 * Create multiple pages with sequential IDs
	 */
	createMultiplePages(count: number): PageSettings[] {
		const pages: PageSettings[] = [];

		for (let i = 0; i < count; i++) {
			const isLandscape = i % 3 === 1; // Every third page is landscape
			pages.push(
				isLandscape
					? PageFactory.createLandscapePage({ id: `page-${i + 1}` })
					: PageFactory.createPage({ id: `page-${i + 1}` }),
			);
		}

		return pages;
	},

	/**
	 * Create a page with specific format
	 */
	createPageWithFormat(format: string): PageSettings {
		const formatDimensions: Record<string, { width: number; height: number }> =
			{
				A4: { width: 210, height: 297 },
				A3: { width: 297, height: 420 },
				Letter: { width: 216, height: 279 },
				Legal: { width: 216, height: 356 },
			};

		const dimensions = formatDimensions[format] || formatDimensions.A4;

		return PageFactory.createPage({
			format,
			...dimensions,
		});
	},
};

// Editor State Factory
export const EditorStateFactory = {
	/**
	 * Create an empty editor state
	 */
	createEmptyState(): EditorState {
		return {
			pages: [],
			elements: [],
		};
	},

	/**
	 * Create a simple editor state with one page and basic elements
	 */
	createSimpleState(): EditorState {
		const page = PageFactory.createPage({ id: "page-1" });
		const elements = [
			ElementFactory.createTextElement({ currentPageId: "page-1" }),
			ElementFactory.createShapeElement({ currentPageId: "page-1" }),
		];

		return {
			pages: [page],
			elements,
		};
	},

	/**
	 * Create a complex editor state with multiple pages and various elements
	 */
	createComplexState(): EditorState {
		const pages = PageFactory.createMultiplePages(3);
		const elements: Element[] = [];

		// Add elements to each page
		pages.forEach((page, index) => {
			const pageElements = ElementFactory.createElementsOnPage(
				page.id,
				4 + index,
			);
			elements.push(...pageElements);

			// Add a block element to the first page
			if (index === 0) {
				elements.push(
					ElementFactory.createBlockElement({ currentPageId: page.id }),
				);
			}

			// Add an address element to the second page
			if (index === 1) {
				elements.push(
					ElementFactory.createAddressElement({ currentPageId: page.id }),
				);
			}
		});

		return {
			pages,
			elements,
		};
	},

	/**
	 * Create an editor state with specific configuration
	 */
	createStateWithConfig(config: {
		pageCount?: number;
		elementsPerPage?: number;
		includeBlocks?: boolean;
		includeAddresses?: boolean;
	}): EditorState {
		const {
			pageCount = 2,
			elementsPerPage = 3,
			includeBlocks = false,
			includeAddresses = false,
		} = config;

		const pages = PageFactory.createMultiplePages(pageCount);
		const elements: Element[] = [];

		pages.forEach((page, index) => {
			// Add regular elements
			const pageElements = ElementFactory.createElementsOnPage(
				page.id,
				elementsPerPage,
			);
			elements.push(...pageElements);

			// Add blocks if requested
			if (includeBlocks && index === 0) {
				elements.push(
					ElementFactory.createBlockElement({ currentPageId: page.id }),
				);
			}

			// Add addresses if requested
			if (includeAddresses && index < 2) {
				elements.push(
					ElementFactory.createAddressElement({ currentPageId: page.id }),
				);
			}
		});

		return {
			pages,
			elements,
		};
	},

	/**
	 * Create an editor state for testing specific scenarios
	 */
	createTestScenario(
		scenario: "empty" | "single-page" | "multi-page" | "complex",
	): EditorState {
		switch (scenario) {
			case "empty":
				return EditorStateFactory.createEmptyState();
			case "single-page":
				return EditorStateFactory.createSimpleState();
			case "multi-page":
				return EditorStateFactory.createStateWithConfig({
					pageCount: 3,
					elementsPerPage: 2,
				});
			case "complex":
				return EditorStateFactory.createComplexState();
			default:
				return EditorStateFactory.createSimpleState();
		}
	},
};

// Export convenience functions
export const createMockElement = ElementFactory.createTextElement;
export const createMockPage = PageFactory.createPage;
export const createMockEditorState = EditorStateFactory.createSimpleState;
